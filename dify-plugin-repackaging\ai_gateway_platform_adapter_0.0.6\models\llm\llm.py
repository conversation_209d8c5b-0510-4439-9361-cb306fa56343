import base64
import hashlib
import hmac
import json
import logging
from datetime import datetime, timezone
from decimal import Decimal
from typing import Generator, Mapping, Optional, Union, Any
import httpx
from dify_plugin.entities.model import (
    AIModelEntity,
    FetchFrom,
    I18nObject,
    ModelFeature,
    ModelPropertyKey,
    ModelType,
    ParameterRule,
    ParameterType,
)
from dify_plugin.entities.model.llm import LLMResult, LLMResultChunk, LLMResultChunkDelta, LLMUsage
from dify_plugin.entities.model.message import (
    PromptMessage,
    PromptMessageContent,
    PromptMessageContentType,
    PromptMessageTool,
    PromptMessageRole,
    AssistantPromptMessage,
    TextPromptMessageContent,
    ImagePromptMessageContent,
)
from dify_plugin.interfaces.model.large_language_model import LargeLanguageModel
from dify_plugin.errors.model import (
    CredentialsValidateFailedError,
    InvokeError, InvokeConnectionError, InvokeServerUnavailableError, InvokeRateLimitError, InvokeAuthorizationError,
    InvokeBadRequestError,
)

# Setup logger
logger = logging.getLogger(__name__)

class AiGatewayPlatformLargeLanguageModel(LargeLanguageModel):
    """
    AI Gateway Platform Large Language Model with HMAC-SHA256 authentication
    """
    def get_customizable_model_schema(self, model: str, credentials: Mapping) -> AIModelEntity:
        # 1. Extract static properties from credentials
        context_size = int(credentials.get("context_size", 32000))
        display_name = credentials.get("display_name") or model
        vision_support = credentials.get("vision_support", "no_support")

        # 2. Define runtime parameter rules in code
        runtime_parameter_rules = [
            ParameterRule(name='temperature', label=I18nObject(en_US='Temperature', zh_Hans='温度'), type=ParameterType.FLOAT, default=0.8, min=0.0, max=1.0),
            ParameterRule(name='top_p', label=I18nObject(en_US='Top P', zh_Hans='Top P'), type=ParameterType.FLOAT, default=1.0, min=0.0, max=1.0),
            ParameterRule(name='top_k', label=I18nObject(en_US='Top K', zh_Hans='Top K'), type=ParameterType.INT, default=50, min=1, max=100),
            ParameterRule(name='max_tokens', label=I18nObject(en_US='Max Tokens', zh_Hans='最大 Token 数'), type=ParameterType.INT, default=4096, min=1, max=context_size),
            ParameterRule(name='presence_penalty', label=I18nObject(en_US='Presence Penalty', zh_Hans='存在惩罚'), type=ParameterType.FLOAT, default=0.0, min=0.0, max=1.0),
            ParameterRule(name='frequency_penalty', label=I18nObject(en_US='Frequency Penalty', zh_Hans='频率惩罚'), type=ParameterType.FLOAT, default=0.0, min=0.0, max=1.0),
            # ParameterRule(name='repetition_penalty', label=I18nObject(en_US='Repetition Penalty', zh_Hans='重复惩罚'), type=ParameterType.FLOAT, default=1.0, min=0.0, max=2.0),
            # ParameterRule(name='seed', label=I18nObject(en_US='Random Seed', zh_Hans='随机种子'), type=ParameterType.INT, default=0, min=0, max=18446744073709551615),
        ]

        # 3. Build and return the AIModelEntity instance
        return AIModelEntity(
            model=model,
            label=I18nObject(
                en_US=display_name,
                zh_Hans=display_name
            ),
            model_type=ModelType.LLM,
            fetch_from=FetchFrom.CUSTOMIZABLE_MODEL,
            model_properties={
                ModelPropertyKey.CONTEXT_SIZE: context_size,
                ModelPropertyKey.MODE: "chat",
            },
            parameter_rules=runtime_parameter_rules,
            features=[ModelFeature.VISION, ModelFeature.TOOL_CALL, ModelFeature.STREAM_TOOL_CALL, ModelFeature.MULTI_TOOL_CALL] if vision_support == 'support' else [ModelFeature.TOOL_CALL, ModelFeature.STREAM_TOOL_CALL, ModelFeature.MULTI_TOOL_CALL]
        )

    def _generate_hmac_auth(self, customer_code: str, secret_key: str) -> dict:
        """
        Generate HMAC-SHA256 authentication headers
        """
        gmt_format = "%a, %d %b %Y %H:%M:%S GMT"
        x_date = datetime.now(timezone.utc).strftime(gmt_format)
        str_to_sign = f"x-date: {x_date}"

        signature = base64.b64encode(
            hmac.new(secret_key.encode(), str_to_sign.encode(), hashlib.sha256).digest()
        ).decode()

        auth_header = f'hmac username="{customer_code}", algorithm="hmac-sha256", headers="x-date", signature="{signature}"'

        return {
            "x-date": x_date,
            "Authorization": auth_header,
            "Content-Type": "application/json"
        }

    def _convert_messages_to_platform_format(self, messages: list[PromptMessage]) -> list[dict]:
        """
        Convert dify PromptMessage to platform format, including vision/image_url support
        """
        converted_messages = []

        for message in messages:
            msg_dict = {
                "role": message.role.value,
                "content": None
            }

            # Handle different content types
            if isinstance(message.content, str):
                msg_dict["content"] = message.content
            elif isinstance(message.content, list):
                # 支持 vision/image_url + text 混合
                content_parts = []
                for content_item in message.content:
                    if hasattr(content_item, "type"):
                        if content_item.type == PromptMessageContentType.TEXT:
                            content_parts.append({"type": "text", "text": content_item.data})
                        elif content_item.type == PromptMessageContentType.IMAGE:
                            # 对于图片类型，使用 data 属性（它会返回 URL 或 base64 数据）
                            content_parts.append({"type": "image_url", "image_url": {"url": content_item.data}})
                msg_dict["content"] = content_parts

            converted_messages.append(msg_dict)

        return converted_messages

    def _convert_tools_to_platform_format(self, tools: Optional[list[PromptMessageTool]]) -> Optional[list[dict]]:
        """
        Convert Dify tools to platform format
        """
        if not tools:
            return None

        platform_tools = []
        for tool in tools:
            platform_tool = {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description or "",
                    "parameters": tool.parameters or {}
                }
            }
            platform_tools.append(platform_tool)

        return platform_tools

    def _invoke(
        self,
        model: str,
        credentials: Mapping,
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
        tools: Optional[list[PromptMessageTool]] = None,
        stop: Optional[list[str]] = None,
        stream: bool = True,
        user: Optional[str] = None,
    )  -> Union[LLMResult, Generator]:
        """
        Invoke the custom platform API
        """
        # Extract credentials
        api_endpoint = credentials.get("api_endpoint")
        customer_code = credentials.get("customer_code")
        secret_key = credentials.get("secret_key")

        if not all([api_endpoint, customer_code, secret_key]):
            raise CredentialsValidateFailedError("Missing required credentials: api_endpoint, customer_code, or secret_key")

        # Generate authentication headers
        auth_headers = self._generate_hmac_auth(customer_code, secret_key)

        # Convert messages to platform format
        platform_messages = self._convert_messages_to_platform_format(prompt_messages)

        # Convert tools to platform format
        platform_tools = self._convert_tools_to_platform_format(tools)

        # Construct request body for platform API
        request_body = {
            "componentCode": model,  # Use model name as componentCode
            "model": model,  # Add model field for compatibility
            "messages": platform_messages,
            "stream": stream
        }

        # Add model parameters if provided
        if model_parameters.get("temperature") is not None:
            request_body["temperature"] = model_parameters["temperature"]
        if model_parameters.get("max_tokens") is not None:
            request_body["max_tokens"] = model_parameters["max_tokens"]
        if model_parameters.get("top_p") is not None:
            request_body["top_p"] = model_parameters["top_p"]
        if model_parameters.get("top_k") is not None:
            request_body["top_k"] = model_parameters["top_k"]
        if model_parameters.get("presence_penalty") is not None:
            request_body["presence_penalty"] = model_parameters["presence_penalty"]
        if model_parameters.get("frequency_penalty") is not None:
            request_body["frequency_penalty"] = model_parameters["frequency_penalty"]
        if model_parameters.get("repetition_penalty") is not None:
            request_body["repetition_penalty"] = model_parameters["repetition_penalty"]
        if model_parameters.get("seed") is not None:
            request_body["seed"] = model_parameters["seed"]
        if model_parameters.get("stream") is not None:
            request_body["stream"] = model_parameters["stream"]
        if model_parameters.get("vision_support") is not None:
            request_body["vision_support"] = model_parameters["vision_support"]
        if model_parameters.get("skip_special_tokens") is not None:
            request_body["skip_special_tokens"] = model_parameters["skip_special_tokens"]
        if model_parameters.get("ignore_eos") is not None:
            request_body["ignore_EOS"] = model_parameters["ignore_eos"]
        if stop:
            request_body["stop"] = stop
        if platform_tools:
            request_body["tools"] = platform_tools
        if model_parameters.get("tool_choice") is not None:
            request_body["tool_choice"] = model_parameters["tool_choice"]

        try:
            client = httpx.Client(timeout=60.0, verify=False)
            def stream_generator():
                with client.stream(
                    "POST", api_endpoint, headers=auth_headers, json=request_body
                ) as response:
                    response.raise_for_status()
                    yield from self._handle_stream_response(response, prompt_messages)
            return stream_generator()

        except httpx.HTTPStatusError as e:
            raise InvokeError(f"API request failed: {e.response.status_code} - {e.response.text}")
        except Exception as e:
            raise InvokeError(f"Request error: {str(e)}")

    def _handle_stream_response(self, response: httpx.Response, prompt_messages: list[PromptMessage]) -> Generator:
        """
        Handle streaming response from the platform API
        """
        buffer = ""

        for chunk in response.iter_text():
            buffer += chunk

            # Process complete lines
            while "\n" in buffer:
                line, buffer = buffer.split("\n", 1)
                line = line.strip()

                if not line:
                    continue

                # Handle SSE format
                if line.startswith("data: "):
                    data = line[6:]  # Remove "data: " prefix

                    if data == "[DONE]":
                        break

                    try:
                        chunk_data = json.loads(data)

                        # Extract content from platform response
                        if "choices" in chunk_data and len(chunk_data["choices"]) > 0:
                            choice = chunk_data["choices"][0]

                            # Handle delta content
                            delta_content = ""
                            tool_calls = []

                            if "delta" in choice:
                                delta = choice["delta"]
                                if "content" in delta and delta["content"]:
                                    delta_content = delta["content"]

                                # Handle tool calls
                                if "tool_calls" in delta and delta["tool_calls"]:
                                    for tool_call in delta["tool_calls"]:
                                        tool_calls.append(AssistantPromptMessage.ToolCall(
                                            id=tool_call.get("id", ""),
                                            type=tool_call.get("type", "function"),
                                            function=AssistantPromptMessage.ToolCall.ToolCallFunction(
                                                name=tool_call.get("function", {}).get("name", ""),
                                                arguments=tool_call.get("function", {}).get("arguments", "")
                                            )
                                        ))

                            # Create chunk
                            usage = None
                            if "usage" in chunk_data:
                                usage_data = chunk_data["usage"]
                                usage = LLMUsage(
                                    prompt_tokens=usage_data.get("prompt_tokens", 0),
                                    prompt_unit_price=Decimal("0.0"),
                                    prompt_price_unit=Decimal("0.0"),
                                    prompt_price=Decimal("0.0"),
                                    completion_tokens=usage_data.get("completion_tokens", 0),
                                    completion_unit_price=Decimal("0.0"),
                                    completion_price_unit=Decimal("0.0"),
                                    completion_price=Decimal("0.0"),
                                    total_tokens=usage_data.get("total_tokens", 0),
                                    total_price=Decimal("0.0"),
                                    currency="USD",
                                    latency=0.0
                                )

                            chunk = LLMResultChunk(
                                model=chunk_data.get("model", ""),
                                system_fingerprint=chunk_data.get("system_fingerprint"),
                                delta=LLMResultChunkDelta(
                                    index=0,
                                    message=AssistantPromptMessage(
                                        role=PromptMessageRole.ASSISTANT,
                                        content=delta_content,
                                        tool_calls=tool_calls
                                    ),
                                    usage=usage
                                )
                            )

                            yield chunk

                    except json.JSONDecodeError:
                        continue

    def _handle_non_stream_response(self, response: httpx.Response, prompt_messages: list[PromptMessage]) -> LLMResult:
        """
        Handle non-streaming response from the platform API
        """
        response_data = response.json()

        if "choices" not in response_data or len(response_data["choices"]) == 0:
            raise InvokeError("Invalid response format: no choices found")

        choice = response_data["choices"][0]
        message = choice.get("message", {})

        # Extract content and tool calls
        content = message.get("content", "")
        tool_calls = []

        if "tool_calls" in message and message["tool_calls"]:
            for tool_call in message["tool_calls"]:
                tool_calls.append(AssistantPromptMessage.ToolCall(
                    id=tool_call.get("id", ""),
                    type=tool_call.get("type", "function"),
                    function=AssistantPromptMessage.ToolCall.ToolCallFunction(
                        name=tool_call.get("function", {}).get("name", ""),
                        arguments=tool_call.get("function", {}).get("arguments", "")
                    )
                ))

        # Extract usage information
        usage_data = response_data.get("usage", {})
        usage = LLMUsage(
            prompt_tokens=usage_data.get("prompt_tokens", 0),
            prompt_unit_price=Decimal("0.0"),
            prompt_price_unit=Decimal("0.0"),
            prompt_price=Decimal("0.0"),
            completion_tokens=usage_data.get("completion_tokens", 0),
            completion_unit_price=Decimal("0.0"),
            completion_price_unit=Decimal("0.0"),
            completion_price=Decimal("0.0"),
            total_tokens=usage_data.get("total_tokens", 0),
            total_price=Decimal("0.0"),
            currency="USD",
            latency=0.0
        )

        return LLMResult(
            model=response_data.get("model", ""),
            message=AssistantPromptMessage(
                role=PromptMessageRole.ASSISTANT,
                content=content,
                tool_calls=tool_calls
            ),
            usage=usage,
            system_fingerprint=response_data.get("system_fingerprint")
        )

    def validate_credentials(self, model: str, credentials: Mapping) -> None:
        """
        Validate the credentials by making a test request
        """
        logger.info("--- [Debug] Starting credentials validation ---")
        logger.info(f"--- [Debug] Received credentials: {credentials}")

        # Extract credentials
        api_endpoint = credentials.get("api_endpoint")
        customer_code = credentials.get("customer_code")
        secret_key = credentials.get("secret_key")

        if not all([api_endpoint, customer_code, secret_key]):
            logger.error("--- [Debug] Missing required credentials ---")
            raise CredentialsValidateFailedError("Missing required credentials: api_endpoint, customer_code, or secret_key")

        # Generate test authentication headers
        try:
            auth_headers = self._generate_hmac_auth(customer_code, secret_key)
            logger.info(f"--- [Debug] Generated auth headers: {auth_headers}")
        except Exception as e:
            logger.error(f"--- [Debug] Failed to generate authentication: {e}")
            raise CredentialsValidateFailedError(f"Failed to generate authentication: {str(e)}")

        # Make a simple test request
        test_request = {
            "componentCode": model,
            "model": model,
            "messages": [{"role": "user", "content": "Hello"}],
            "stream": False,
            "max_tokens": 10
        }
        logger.info(f"--- [Debug] Test request body: {json.dumps(test_request)}")

        try:
            with httpx.Client(timeout=30.0, verify=False) as client:
                response = client.post(
                    api_endpoint,
                    headers=auth_headers,
                    json=test_request,
                )
                logger.info(f"--- [Debug] API Response Status: {response.status_code}")
                logger.info(f"--- [Debug] API Response Body: {response.text}")
                response.raise_for_status()

                # Validate response format
                response_data = response.json()
                if "choices" not in response_data:
                    raise CredentialsValidateFailedError("Invalid response format: no choices found")

                logger.info("--- [Debug] Credentials validation successful ---")

        except httpx.HTTPStatusError as e:
            logger.error(f"--- [Debug] API request failed with status {e.response.status_code}: {e.response.text}")
            if e.response.status_code == 401:
                raise CredentialsValidateFailedError("Authentication failed: invalid credentials")
            elif e.response.status_code == 403:
                raise CredentialsValidateFailedError("Access denied: insufficient permissions")
            else:
                raise CredentialsValidateFailedError(f"API request failed: {e.response.status_code}")
        except Exception as e:
            logger.error(f"--- [Debug] Validation error: {e}")
            raise CredentialsValidateFailedError(f"Validation error: {str(e)}")

    def get_num_tokens(self, model: str, credentials: Mapping, prompt_messages: list[PromptMessage], tools: Optional[list[PromptMessageTool]] = None) -> int:
        """
        Get number of tokens for given prompt messages
        For now, return 0 as the platform doesn't provide token counting
        """
        return 0

    @property
    def _invoke_error_mapping(self) -> dict[type[InvokeError], list[type[Exception]]]:
        """
        Map model invoke error to unified error
        """
        return {
            InvokeConnectionError: [httpx.ConnectError, httpx.TimeoutException],
            InvokeServerUnavailableError: [httpx.HTTPStatusError],  # 5xx errors
            InvokeRateLimitError: [httpx.HTTPStatusError],  # 429 errors
            InvokeAuthorizationError: [httpx.HTTPStatusError],  # 401, 403 errors
            InvokeBadRequestError: [httpx.HTTPStatusError],  # 4xx errors (except 401, 403, 429)
        }

def main():
    """
    主测试函数，用于演示和验证 AiGatewayPlatformLargeLanguageModel 的功能。
    """
    import sys
    import os
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', '..')))
    # --- 1. 配置凭据和模型信息 ---
    CREDENTIALS = {
        "api_endpoint": "https://10.147.241.32:7010/ai-gateway/predict",
        "customer_code": "1000401500012",
        "secret_key": "557591a651b94ab2bda5a0b319c20e83",
    }
    MODEL_NAME = "150005"

    model_schemas_for_test = [  # <--- 从字典 {} 改为列表 []
        AIModelEntity(
            model=MODEL_NAME,
            label=I18nObject(en_US=MODEL_NAME, zh_Hans=MODEL_NAME),
            model_type=ModelType.LLM,
            features=[
                ModelFeature.MULTI_TOOL_CALL,
                ModelFeature.TOOL_CALL,
                ModelFeature.STREAM_TOOL_CALL,
                ModelFeature.VISION,
            ],
            # fetch_from=AIModelEntity.FetchFrom.CUSTOMIZABLE_MODEL, # 建议加上这个，与 get_customizable_model_schema 保持一致
            model_properties={
                "context_size": int(CREDENTIALS.get("context_size", 4096)),
                "max_chunks": 1,
            }
        )
    ]
    llm = AiGatewayPlatformLargeLanguageModel(model_schemas=model_schemas_for_test)

    print("--- 开始测试 AI Gateway Platform Large Language Model ---")
    print(f"API Endpoint: {CREDENTIALS['api_endpoint']}")
    print(f"Model/Component Code: {MODEL_NAME}\n")

    # --- 2. 测试凭据验证 ---
    print("--- [Test 1] 凭据验证 ---")
    try:
        # 凭据验证测试
        llm.validate_credentials(model=MODEL_NAME, credentials=CREDENTIALS)
        print("✅ 凭据验证成功!\n")
    except Exception as e:
        print(f"❌ 凭据验证失败: {e}\n")
        return

    # --- 4. 测试流式调用 ---
    print("--- [Test 3] 流式调用 ---")
    try:
        # --- 更正点: 使用 PromptMessageRole.USER 枚举代替字符串 ---
        messages = [PromptMessage(role=PromptMessageRole.USER, content="请用中文写一个关于太空探索的短故事。")]
        response_generator = llm.invoke(
            model=MODEL_NAME,
            credentials=CREDENTIALS,
            prompt_messages=messages,
            model_parameters={"max_tokens": 200, "temperature": 0.7},
            stream=True
        )
        print("✅ 流式调用成功! 模型回复:")
        full_response = ""
        for chunk in response_generator:
            if chunk.delta.message and chunk.delta.message.content:
                content_piece = chunk.delta.message.content
                print(content_piece, end="", flush=True)
                full_response += content_piece
                print(content_piece)
        print("\n--- 流式输出结束 ---\n")
    except Exception as e:
        print(f"❌ 流式调用失败: {e}\n")

    # --- 5. 测试 Vision (多模态) 调用 ---
    print("--- [Test 4] Vision (多模态) 调用 ---")
    try:
        base64_image = "R0lGODlhAQABAIAsAAAAAP//-w=='"
        vision_messages = [
            PromptMessage(
                # --- 更正点: 使用 PromptMessageRole.USER 枚举代替字符串 ---
                role=PromptMessageRole.USER,
                content=[
                    TextPromptMessageContent(data="这张图片里有什么？请用中文回答。"),
                    ImagePromptMessageContent(
                        format='gif',
                        mime_type='image/gif',
                        base64_data=base64_image
                    )
                ]
            )
        ]

        result = llm.invoke(
            model=MODEL_NAME,
            credentials=CREDENTIALS,
            prompt_messages=vision_messages,
            model_parameters={"max_tokens": 100},
            stream=False
        )

        if isinstance(result, LLMResult):
            print("✅ Vision 调用成功!")
            print(f"模型回复: {result.message.content}\n")

    except Exception as e:
        print(f"❌ Vision 调用失败: {e}\n")

    # --- 6. 测试工具调用 (Tool Calling) ---
    print("--- [Test 5] 工具调用 (Tool Calling) ---")
    try:
        tools = [
            PromptMessageTool(
                name="get_current_weather",
                description="获取给定地点的当前天气",
                parameters={
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "城市和省份, e.g. 北京市",
                        },
                        "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]},
                    },
                    "required": ["location"],
                },
            )
        ]
        # --- 更正点: 使用 PromptMessageRole.USER 枚举代替字符串 ---
        messages = [PromptMessage(role=PromptMessageRole.USER, content="波士顿现在天气怎么样?")]
        result = llm.invoke(
            model=MODEL_NAME,
            credentials=CREDENTIALS,
            prompt_messages=messages,
            tools=tools,
            model_parameters={"max_tokens": 100},
            stream=False
        )

        if isinstance(result, LLMResult):
            print("✅ 工具调用请求成功!")
            if result.message.tool_calls:
                print("检测到工具调用:")
                for tc in result.message.tool_calls:
                    print(f"  - 函数名称: {tc.function.name}")
                    print(f"  - 函数参数: {tc.function.arguments}")
            else:
                print("模型未生成工具调用，而是直接回复:")
                print(f"  {result.message.content}")
            print("")

    except Exception as e:
        print(f"❌ 工具调用失败: {e}\n")



if __name__ == '__main__':
    main()
