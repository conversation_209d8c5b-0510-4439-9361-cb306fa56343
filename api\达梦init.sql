CREATE SEQUENCE "DIFY"."INVITATION_CODES_ID_SEQ"
                          INCREMENT BY 1
                          START WITH 1
                          MAXVALUE **********
                          MINVALUE 1
                          NOCYCLE
                          NOCACHE
                          NOORDER;

CREATE SEQUENCE "DIFY"."TASK_ID_SEQUENCE"
                          INCREMENT BY 1
                          START WITH 1
                          MAXVALUE **********
                          MINVALUE 1
                          NOCYCLE
                          NOCACHE
                          NOORDER;

CREATE SEQUENCE "DIFY"."TASKSET_ID_SEQUENCE"
                          INCREMENT BY 1
                          START WITH 1
                          MAXVALUE **********
                          MINVALUE 1
                          NOCYCLE
                          NOCACHE
                          NOORDER;



CREATE TABLE "DIFY"."ACCOUNT_INTEGRATES"
(
"ID" VARCHAR(50) NOT NULL,
"ACCOUNT_ID" VARCHAR(50) NOT NULL,
"PROVIDER" VARCHAR(16 CHAR) NOT NULL,
"OPEN_ID" VARCHAR(255 CHAR) NOT NULL,
"ENCRYPTED_TOKEN" VARCHAR(255 CHAR) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "ACCOUNT_INTEGRATE_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_ACCOUNT_PROVIDER" UNIQUE("ACCOUNT_ID", "PROVIDER"),
CONSTRAINT "UNIQUE_PROVIDER_OPEN_ID" UNIQUE("OPEN_ID", "PROVIDER")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."ACCOUNT_PLUGIN_PERMISSIONS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"INSTALL_PERMISSION" VARCHAR(16 CHAR) DEFAULT 'everyone' NOT NULL,
"DEBUG_PERMISSION" VARCHAR(16 CHAR) DEFAULT 'noone' NOT NULL,
CONSTRAINT "ACCOUNT_PLUGIN_PERMISSION_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_TENANT_PLUGIN" UNIQUE("TENANT_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."ACCOUNTS"
(
"ID" VARCHAR(50) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"EMAIL" VARCHAR(255 CHAR) NOT NULL,
"PASSWORD" VARCHAR(255 CHAR),
"PASSWORD_SALT" VARCHAR(255 CHAR),
"AVATAR" VARCHAR(255 CHAR),
"INTERFACE_LANGUAGE" VARCHAR(255 CHAR),
"INTERFACE_THEME" VARCHAR(255 CHAR),
"TIMEZONE" VARCHAR(255 CHAR),
"LAST_LOGIN_AT" TIMESTAMP(6),
"LAST_LOGIN_IP" VARCHAR(255 CHAR),
"LAST_ACTIVE_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"STATUS" VARCHAR(16 CHAR) DEFAULT 'active' NOT NULL,
"INITIALIZED_AT" TIMESTAMP(6),
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"USER_ID" VARCHAR(36 CHAR),
"USER_TYPE" VARCHAR(256 CHAR) DEFAULT 'EMPLOYEE',
"START_DATE" DATE,
"END_DATE" DATE,
CONSTRAINT "ACCOUNT_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_USER_ID" UNIQUE("USER_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."ACCOUNT_EMAIL_IDX" ON "DIFY"."ACCOUNTS"("EMAIL" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."ALEMBIC_VERSION"
(
"VERSION_NUM" VARCHAR(32 CHAR) NOT NULL,
CONSTRAINT "ALEMBIC_VERSION_PKC" NOT CLUSTER PRIMARY KEY("VERSION_NUM")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."API_BASED_EXTENSIONS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"API_ENDPOINT" VARCHAR(255 CHAR) NOT NULL,
"API_KEY" TEXT NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "API_BASED_EXTENSION_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."API_BASED_EXTENSION_TENANT_IDX" ON "DIFY"."API_BASED_EXTENSIONS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."API_REQUESTS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"API_TOKEN_ID" VARCHAR(50) NOT NULL,
"PATH" VARCHAR(255 CHAR) NOT NULL,
"REQUEST" TEXT,
"RESPONSE" TEXT,
"IP" VARCHAR(255 CHAR) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "API_REQUEST_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."API_REQUEST_TOKEN_IDX" ON "DIFY"."API_REQUESTS"("API_TOKEN_ID" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."API_TOKENS"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50),
"TYPE" VARCHAR(16 CHAR) NOT NULL,
"TOKEN" VARCHAR(255 CHAR) NOT NULL,
"LAST_USED_AT" TIMESTAMP(6),
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"TENANT_ID" VARCHAR(50),
CONSTRAINT "API_TOKEN_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."API_TOKEN_TOKEN_IDX" ON "DIFY"."API_TOKENS"("TOKEN" ASC,"TYPE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."API_TOKEN_APP_ID_TYPE_IDX" ON "DIFY"."API_TOKENS"("APP_ID" ASC,"TYPE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."API_TOKEN_TENANT_IDX" ON "DIFY"."API_TOKENS"("TENANT_ID" ASC,"TYPE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."APP_ANNOTATION_HIT_HISTORIES"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"ANNOTATION_ID" VARCHAR(50) NOT NULL,
"SOURCE" TEXT NOT NULL,
"QUESTION" TEXT NOT NULL,
"ACCOUNT_ID" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"SCORE" DOUBLE DEFAULT 0 NOT NULL,
"MESSAGE_ID" VARCHAR(50) NOT NULL,
"ANNOTATION_QUESTION" TEXT NOT NULL,
"ANNOTATION_CONTENT" TEXT NOT NULL,
CONSTRAINT "APP_ANNOTATION_HIT_HISTORIES_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."APP_ANNOTATION_HIT_HISTORIES_MESSAGE_IDX" ON "DIFY"."APP_ANNOTATION_HIT_HISTORIES"("MESSAGE_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."APP_ANNOTATION_HIT_HISTORIES_ANNOTATION_IDX" ON "DIFY"."APP_ANNOTATION_HIT_HISTORIES"("ANNOTATION_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."APP_ANNOTATION_HIT_HISTORIES_ACCOUNT_IDX" ON "DIFY"."APP_ANNOTATION_HIT_HISTORIES"("ACCOUNT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."APP_ANNOTATION_HIT_HISTORIES_APP_IDX" ON "DIFY"."APP_ANNOTATION_HIT_HISTORIES"("APP_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."APP_ANNOTATION_SETTINGS"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"SCORE_THRESHOLD" DOUBLE DEFAULT 0 NOT NULL,
"COLLECTION_BINDING_ID" VARCHAR(50) NOT NULL,
"CREATED_USER_ID" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_USER_ID" VARCHAR(50) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "APP_ANNOTATION_SETTINGS_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."APP_ANNOTATION_SETTINGS_APP_IDX" ON "DIFY"."APP_ANNOTATION_SETTINGS"("APP_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."APP_DATASET_JOINS"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
CONSTRAINT "APP_DATASET_JOIN_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."APP_DATASET_JOIN_APP_DATASET_IDX" ON "DIFY"."APP_DATASET_JOINS"("APP_ID" ASC,"DATASET_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."APP_MCP_SERVERS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"DESCRIPTION" VARCHAR(255 CHAR) NOT NULL,
"SERVER_CODE" VARCHAR(255 CHAR) NOT NULL,
"STATUS" VARCHAR(255 CHAR) DEFAULT 'normal' NOT NULL,
"PARAMETERS" TEXT NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
CONSTRAINT "APP_MCP_SERVER_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_APP_MCP_SERVER_TENANT_APP_ID" UNIQUE("APP_ID", "TENANT_ID"),
CONSTRAINT "UNIQUE_APP_MCP_SERVER_SERVER_CODE" UNIQUE("SERVER_CODE")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."APP_MODEL_CONFIGS"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"PROVIDER" VARCHAR(255 CHAR),
"MODEL_ID" VARCHAR(255 CHAR),
"CONFIGS" VARCHAR(32767),
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"OPENING_STATEMENT" TEXT,
"SUGGESTED_QUESTIONS" TEXT,
"SUGGESTED_QUESTIONS_AFTER_ANSWER" TEXT,
"MORE_LIKE_THIS" TEXT,
"MODEL" TEXT,
"USER_INPUT_FORM" TEXT,
"PRE_PROMPT" TEXT,
"AGENT_MODE" TEXT,
"SPEECH_TO_TEXT" TEXT,
"SENSITIVE_WORD_AVOIDANCE" TEXT,
"RETRIEVER_RESOURCE" TEXT,
"DATASET_QUERY_VARIABLE" VARCHAR(255 CHAR),
"PROMPT_TYPE" VARCHAR(255 CHAR) DEFAULT 'simple' NOT NULL,
"CHAT_PROMPT_CONFIG" TEXT,
"COMPLETION_PROMPT_CONFIG" TEXT,
"DATASET_CONFIGS" TEXT,
"EXTERNAL_DATA_TOOLS" TEXT,
"FILE_UPLOAD" TEXT,
"TEXT_TO_SPEECH" TEXT,
"CREATED_BY" VARCHAR(50),
"UPDATED_BY" VARCHAR(50),
CONSTRAINT "APP_MODEL_CONFIG_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."APP_APP_ID_IDX" ON "DIFY"."APP_MODEL_CONFIGS"("APP_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."APPS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"MODE" VARCHAR(255 CHAR) NOT NULL,
"ICON" VARCHAR(255 CHAR),
"ICON_BACKGROUND" VARCHAR(255 CHAR),
"APP_MODEL_CONFIG_ID" VARCHAR(50),
"STATUS" VARCHAR(255 CHAR) DEFAULT 'normal' NOT NULL,
"ENABLE_SITE" BIT NOT NULL,
"ENABLE_API" BIT NOT NULL,
"API_RPM" INT DEFAULT 0 NOT NULL,
"API_RPH" INT DEFAULT 0 NOT NULL,
"IS_DEMO" BIT DEFAULT 0 NOT NULL,
"IS_PUBLIC" BIT DEFAULT 0 NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"IS_UNIVERSAL" BIT DEFAULT 0 NOT NULL,
"WORKFLOW_ID" VARCHAR(50),
"DESCRIPTION" TEXT DEFAULT '' NOT NULL,
"TRACING" TEXT,
"MAX_ACTIVE_REQUESTS" INT,
"ICON_TYPE" VARCHAR(255 CHAR),
"CREATED_BY" VARCHAR(50),
"UPDATED_BY" VARCHAR(50),
"USE_ICON_AS_ANSWER_ICON" BIT DEFAULT 0 NOT NULL,
CONSTRAINT "APP_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."APP_TENANT_ID_IDX" ON "DIFY"."APPS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."CELERY_TASKMETA"
(
"ID" INT DEFAULT "DIFY"."TASK_ID_SEQUENCE".NEXTVAL NOT NULL,
"TASK_ID" VARCHAR(155 CHAR),
"STATUS" VARCHAR(50 CHAR),
"RESULT" BLOB,
"DATE_DONE" TIMESTAMP(6),
"TRACEBACK" TEXT,
"NAME" VARCHAR(155 CHAR),
"ARGS" BLOB,
"KWARGS" BLOB,
"WORKER" VARCHAR(155 CHAR),
"RETRIES" INT,
"QUEUE" VARCHAR(155 CHAR),
CONSTRAINT "CELERY_TASKMETA_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "CELERY_TASKMETA_TASK_ID_KEY" UNIQUE("TASK_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."CELERY_TASKSETMETA"
(
"ID" INT DEFAULT "DIFY"."TASKSET_ID_SEQUENCE".NEXTVAL NOT NULL,
"TASKSET_ID" VARCHAR(155 CHAR),
"RESULT" BLOB,
"DATE_DONE" TIMESTAMP(6),
CONSTRAINT "CELERY_TASKSETMETA_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "CELERY_TASKSETMETA_TASKSET_ID_KEY" UNIQUE("TASKSET_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."CHILD_CHUNKS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"DOCUMENT_ID" VARCHAR(50) NOT NULL,
"SEGMENT_ID" VARCHAR(50) NOT NULL,
"POSITION" INT NOT NULL,
"CONTENT" TEXT NOT NULL,
"WORD_COUNT" INT NOT NULL,
"INDEX_NODE_ID" VARCHAR(255 CHAR),
"INDEX_NODE_HASH" VARCHAR(255 CHAR),
"TYPE" VARCHAR(255 CHAR) DEFAULT 'AUTOMATIC' NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_BY" VARCHAR(50),
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"INDEXING_AT" TIMESTAMP(6),
"COMPLETED_AT" TIMESTAMP(6),
"ERROR" TEXT,
CONSTRAINT "CHILD_CHUNK_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."CHILD_CHUNKS_NODE_IDX" ON "DIFY"."CHILD_CHUNKS"("DATASET_ID" ASC,"INDEX_NODE_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."CHILD_CHUNKS_SEGMENT_IDX" ON "DIFY"."CHILD_CHUNKS"("SEGMENT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."CHILD_CHUNK_DATASET_ID_IDX" ON "DIFY"."CHILD_CHUNKS"("DATASET_ID" ASC,"DOCUMENT_ID" ASC,"INDEX_NODE_ID" ASC,"SEGMENT_ID" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."CONVERSATIONS"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"APP_MODEL_CONFIG_ID" VARCHAR(50),
"MODEL_PROVIDER" VARCHAR(255 CHAR),
"OVERRIDE_MODEL_CONFIGS" TEXT,
"MODEL_ID" VARCHAR(255 CHAR),
"MODE" VARCHAR(255 CHAR) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"SUMMARY" TEXT,
"INPUTS" VARCHAR(32767) NOT NULL,
"INTRODUCTION" TEXT,
"SYSTEM_INSTRUCTION" TEXT,
"SYSTEM_INSTRUCTION_TOKENS" INT DEFAULT 0 NOT NULL,
"STATUS" VARCHAR(255 CHAR) NOT NULL,
"FROM_SOURCE" VARCHAR(255 CHAR) NOT NULL,
"FROM_END_USER_ID" VARCHAR(50),
"FROM_ACCOUNT_ID" VARCHAR(50),
"READ_AT" TIMESTAMP(6),
"READ_ACCOUNT_ID" VARCHAR(50),
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"IS_DELETED" BIT DEFAULT 0 NOT NULL,
"INVOKE_FROM" VARCHAR(255 CHAR),
"DIALOGUE_COUNT" INT DEFAULT 0 NOT NULL,
CONSTRAINT "CONVERSATION_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."CONVERSATION_APP_FROM_USER_IDX" ON "DIFY"."CONVERSATIONS"("APP_ID" ASC,"FROM_END_USER_ID" ASC,"FROM_SOURCE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DATA_SOURCE_API_KEY_AUTH_BINDINGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"CATEGORY" VARCHAR(255 CHAR) NOT NULL,
"PROVIDER" VARCHAR(255 CHAR) NOT NULL,
"CREDENTIALS" TEXT,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"DISABLED" BIT DEFAULT 0,
CONSTRAINT "DATA_SOURCE_API_KEY_AUTH_BINDING_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATA_SOURCE_API_KEY_AUTH_BINDING_TENANT_ID_IDX" ON "DIFY"."DATA_SOURCE_API_KEY_AUTH_BINDINGS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATA_SOURCE_API_KEY_AUTH_BINDING_PROVIDER_IDX" ON "DIFY"."DATA_SOURCE_API_KEY_AUTH_BINDINGS"("PROVIDER" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DATA_SOURCE_OAUTH_BINDINGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"ACCESS_TOKEN" VARCHAR(255 CHAR) NOT NULL,
"PROVIDER" VARCHAR(255 CHAR) NOT NULL,
"SOURCE_INFO" VARCHAR(32767) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"DISABLED" BIT DEFAULT 0,
CONSTRAINT "SOURCE_BINDING_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."SOURCE_INFO_IDX" ON "DIFY"."DATA_SOURCE_OAUTH_BINDINGS"("SOURCE_INFO" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."SOURCE_BINDING_TENANT_ID_IDX" ON "DIFY"."DATA_SOURCE_OAUTH_BINDINGS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DATASET_AUTO_DISABLE_LOGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"DOCUMENT_ID" VARCHAR(50) NOT NULL,
"NOTIFIED" BIT DEFAULT 0 NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "DATASET_AUTO_DISABLE_LOG_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_AUTO_DISABLE_LOG_CREATED_ATX" ON "DIFY"."DATASET_AUTO_DISABLE_LOGS"("CREATED_AT" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_AUTO_DISABLE_LOG_TENANT_IDX" ON "DIFY"."DATASET_AUTO_DISABLE_LOGS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_AUTO_DISABLE_LOG_DATASET_IDX" ON "DIFY"."DATASET_AUTO_DISABLE_LOGS"("DATASET_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DATASET_COLLECTION_BINDINGS"
(
"ID" VARCHAR(50) NOT NULL,
"PROVIDER_NAME" VARCHAR(255 CHAR) NOT NULL,
"MODEL_NAME" VARCHAR(255 CHAR) NOT NULL,
"COLLECTION_NAME" VARCHAR(64 CHAR) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"TYPE" VARCHAR(40 CHAR) DEFAULT 'dataset' NOT NULL,
CONSTRAINT "DATASET_COLLECTION_BINDINGS_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."PROVIDER_MODEL_NAME_IDX" ON "DIFY"."DATASET_COLLECTION_BINDINGS"("MODEL_NAME" ASC,"PROVIDER_NAME" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DATASET_KEYWORD_TABLES"
(
"ID" VARCHAR(50) NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"KEYWORD_TABLE" TEXT NOT NULL,
"DATA_SOURCE_TYPE" VARCHAR(255 CHAR) DEFAULT 'database' NOT NULL,
CONSTRAINT "DATASET_KEYWORD_TABLE_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "DATASET_KEYWORD_TABLES_DATASET_ID_KEY" UNIQUE("DATASET_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_KEYWORD_TABLE_DATASET_ID_IDX" ON "DIFY"."DATASET_KEYWORD_TABLES"("DATASET_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DATASET_METADATA_BINDINGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"METADATA_ID" VARCHAR(50) NOT NULL,
"DOCUMENT_ID" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
CONSTRAINT "DATASET_METADATA_BINDING_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_METADATA_BINDING_METADATA_IDX" ON "DIFY"."DATASET_METADATA_BINDINGS"("METADATA_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_METADATA_BINDING_DOCUMENT_IDX" ON "DIFY"."DATASET_METADATA_BINDINGS"("DOCUMENT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_METADATA_BINDING_DATASET_IDX" ON "DIFY"."DATASET_METADATA_BINDINGS"("DATASET_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_METADATA_BINDING_TENANT_IDX" ON "DIFY"."DATASET_METADATA_BINDINGS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DATASET_METADATAS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"TYPE" VARCHAR(255 CHAR) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"UPDATED_BY" VARCHAR(50),
CONSTRAINT "DATASET_METADATA_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_METADATA_TENANT_IDX" ON "DIFY"."DATASET_METADATAS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_METADATA_DATASET_IDX" ON "DIFY"."DATASET_METADATAS"("DATASET_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DATASET_PERMISSIONS"
(
"ID" VARCHAR(50) NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"ACCOUNT_ID" VARCHAR(50) NOT NULL,
"HAS_PERMISSION" BIT DEFAULT 1 NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
CONSTRAINT "DATASET_PERMISSION_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."IDX_DATASET_PERMISSIONS_DATASET_ID" ON "DIFY"."DATASET_PERMISSIONS"("DATASET_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."IDX_DATASET_PERMISSIONS_ACCOUNT_ID" ON "DIFY"."DATASET_PERMISSIONS"("ACCOUNT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."IDX_DATASET_PERMISSIONS_TENANT_ID" ON "DIFY"."DATASET_PERMISSIONS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DATASET_PROCESS_RULES"
(
"ID" VARCHAR(50) NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"MODE" VARCHAR(255 CHAR) DEFAULT 'automatic' NOT NULL,
"RULES" TEXT,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "DATASET_PROCESS_RULE_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_PROCESS_RULE_DATASET_ID_IDX" ON "DIFY"."DATASET_PROCESS_RULES"("DATASET_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DATASET_QUERIES"
(
"ID" VARCHAR(50) NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"CONTENT" TEXT NOT NULL,
"SOURCE" VARCHAR(255 CHAR) NOT NULL,
"SOURCE_APP_ID" VARCHAR(50),
"CREATED_BY_ROLE" VARCHAR(32767 CHAR) NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
CONSTRAINT "DATASET_QUERY_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_QUERY_DATASET_ID_IDX" ON "DIFY"."DATASET_QUERIES"("DATASET_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DATASET_RETRIEVER_RESOURCES"
(
"ID" VARCHAR(50) NOT NULL,
"MESSAGE_ID" VARCHAR(50) NOT NULL,
"POSITION" INT NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"DATASET_NAME" TEXT NOT NULL,
"DOCUMENT_ID" VARCHAR(50),
"DOCUMENT_NAME" TEXT NOT NULL,
"DATA_SOURCE_TYPE" TEXT,
"SEGMENT_ID" VARCHAR(50),
"SCORE" DOUBLE,
"CONTENT" TEXT NOT NULL,
"HIT_COUNT" INT,
"WORD_COUNT" INT,
"SEGMENT_POSITION" INT,
"INDEX_NODE_HASH" TEXT,
"RETRIEVER_FROM" TEXT NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
CONSTRAINT "DATASET_RETRIEVER_RESOURCE_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_RETRIEVER_RESOURCE_MESSAGE_ID_IDX" ON "DIFY"."DATASET_RETRIEVER_RESOURCES"("MESSAGE_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DATASETS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"DESCRIPTION" TEXT,
"PROVIDER" VARCHAR(255 CHAR) DEFAULT 'vendor' NOT NULL,
"PERMISSION" VARCHAR(255 CHAR) DEFAULT 'only_me' NOT NULL,
"DATA_SOURCE_TYPE" VARCHAR(255 CHAR),
"INDEXING_TECHNIQUE" VARCHAR(255 CHAR),
"INDEX_STRUCT" TEXT,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_BY" VARCHAR(50),
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"EMBEDDING_MODEL" VARCHAR(255 CHAR) DEFAULT 'text-embedding-ada-002',
"EMBEDDING_MODEL_PROVIDER" VARCHAR(255 CHAR) DEFAULT 'openai',
"COLLECTION_BINDING_ID" VARCHAR(50),
"RETRIEVAL_MODEL" VARCHAR(32767),
"BUILT_IN_FIELD_ENABLED" BIT DEFAULT 0 NOT NULL,
CONSTRAINT "DATASET_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."RETRIEVAL_MODEL_IDX" ON "DIFY"."DATASETS"("RETRIEVAL_MODEL" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DATASET_TENANT_IDX" ON "DIFY"."DATASETS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DIFY_SETUPS"
(
"VERSION" VARCHAR(255 CHAR) NOT NULL,
"SETUP_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "DIFY_SETUP_PKEY" NOT CLUSTER PRIMARY KEY("VERSION")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DOCUMENT_SEGMENTS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"DOCUMENT_ID" VARCHAR(50) NOT NULL,
"POSITION" INT NOT NULL,
"CONTENT" TEXT NOT NULL,
"WORD_COUNT" INT NOT NULL,
"TOKENS" INT NOT NULL,
"KEYWORDS" VARCHAR(32767),
"INDEX_NODE_ID" VARCHAR(255 CHAR),
"INDEX_NODE_HASH" VARCHAR(255 CHAR),
"HIT_COUNT" INT NOT NULL,
"ENABLED" BIT DEFAULT 1 NOT NULL,
"DISABLED_AT" TIMESTAMP(6),
"DISABLED_BY" VARCHAR(50),
"STATUS" VARCHAR(255 CHAR) DEFAULT 'waiting' NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"INDEXING_AT" TIMESTAMP(6),
"COMPLETED_AT" TIMESTAMP(6),
"ERROR" TEXT,
"STOPPED_AT" TIMESTAMP(6),
"ANSWER" TEXT,
"UPDATED_BY" VARCHAR(50),
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "DOCUMENT_SEGMENT_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DOCUMENT_SEGMENT_TENANT_DATASET_IDX" ON "DIFY"."DOCUMENT_SEGMENTS"("DATASET_ID" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DOCUMENT_SEGMENT_TENANT_IDX" ON "DIFY"."DOCUMENT_SEGMENTS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DOCUMENT_SEGMENT_TENANT_DOCUMENT_IDX" ON "DIFY"."DOCUMENT_SEGMENTS"("DOCUMENT_ID" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DOCUMENT_SEGMENT_DOCUMENT_ID_IDX" ON "DIFY"."DOCUMENT_SEGMENTS"("DOCUMENT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DOCUMENT_SEGMENT_DATASET_ID_IDX" ON "DIFY"."DOCUMENT_SEGMENTS"("DATASET_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DOCUMENT_SEGMENT_NODE_DATASET_IDX" ON "DIFY"."DOCUMENT_SEGMENTS"("DATASET_ID" ASC,"INDEX_NODE_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."DOCUMENTS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"POSITION" INT NOT NULL,
"DATA_SOURCE_TYPE" VARCHAR(255 CHAR) NOT NULL,
"DATA_SOURCE_INFO" TEXT,
"DATASET_PROCESS_RULE_ID" VARCHAR(50),
"BATCH" VARCHAR(255 CHAR) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"CREATED_FROM" VARCHAR(255 CHAR) NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_API_REQUEST_ID" VARCHAR(50),
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"PROCESSING_STARTED_AT" TIMESTAMP(6),
"FILE_ID" TEXT,
"WORD_COUNT" INT,
"PARSING_COMPLETED_AT" TIMESTAMP(6),
"CLEANING_COMPLETED_AT" TIMESTAMP(6),
"SPLITTING_COMPLETED_AT" TIMESTAMP(6),
"TOKENS" INT,
"INDEXING_LATENCY" DOUBLE,
"COMPLETED_AT" TIMESTAMP(6),
"IS_PAUSED" BIT DEFAULT 0,
"PAUSED_BY" VARCHAR(50),
"PAUSED_AT" TIMESTAMP(6),
"ERROR" TEXT,
"STOPPED_AT" TIMESTAMP(6),
"INDEXING_STATUS" VARCHAR(255 CHAR) DEFAULT 'waiting' NOT NULL,
"ENABLED" BIT DEFAULT 1 NOT NULL,
"DISABLED_AT" TIMESTAMP(6),
"DISABLED_BY" VARCHAR(50),
"ARCHIVED" BIT DEFAULT 0 NOT NULL,
"ARCHIVED_REASON" VARCHAR(255 CHAR),
"ARCHIVED_BY" VARCHAR(50),
"ARCHIVED_AT" TIMESTAMP(6),
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"DOC_TYPE" VARCHAR(40 CHAR),
"DOC_METADATA" VARCHAR(32767),
"DOC_FORM" VARCHAR(255 CHAR) DEFAULT 'text_model' NOT NULL,
"DOC_LANGUAGE" VARCHAR(255 CHAR),
CONSTRAINT "DOCUMENT_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DOCUMENT_DATASET_ID_IDX" ON "DIFY"."DOCUMENTS"("DATASET_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DOCUMENT_TENANT_IDX" ON "DIFY"."DOCUMENTS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DOCUMENT_METADATA_IDX" ON "DIFY"."DOCUMENTS"("DOC_METADATA" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."DOCUMENT_IS_PAUSED_IDX" ON "DIFY"."DOCUMENTS"("IS_PAUSED" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."EMBEDDINGS"
(
"ID" VARCHAR(50) NOT NULL,
"HASH" VARCHAR(64 CHAR) NOT NULL,
"EMBEDDING" BLOB NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"MODEL_NAME" VARCHAR(255 CHAR) DEFAULT 'text-embedding-ada-002' NOT NULL,
"PROVIDER_NAME" VARCHAR(255 CHAR) DEFAULT '' NOT NULL,
CONSTRAINT "EMBEDDING_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "EMBEDDING_HASH_IDX" UNIQUE("HASH", "MODEL_NAME", "PROVIDER_NAME")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."CREATED_AT_IDX" ON "DIFY"."EMBEDDINGS"("CREATED_AT" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."END_USERS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50),
"TYPE" VARCHAR(255 CHAR) NOT NULL,
"EXTERNAL_USER_ID" VARCHAR(255 CHAR),
"NAME" VARCHAR(255 CHAR),
"IS_ANONYMOUS" BIT DEFAULT 1 NOT NULL,
"SESSION_ID" VARCHAR(255 CHAR) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "END_USER_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."END_USER_TENANT_SESSION_ID_IDX" ON "DIFY"."END_USERS"("SESSION_ID" ASC,"TENANT_ID" ASC,"TYPE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."END_USER_SESSION_ID_IDX" ON "DIFY"."END_USERS"("SESSION_ID" ASC,"TYPE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."EXTERNAL_KNOWLEDGE_APIS"
(
"ID" VARCHAR(50) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"DESCRIPTION" VARCHAR(255 CHAR) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"SETTINGS" TEXT,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_BY" VARCHAR(50),
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "EXTERNAL_KNOWLEDGE_APIS_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."EXTERNAL_KNOWLEDGE_APIS_TENANT_IDX" ON "DIFY"."EXTERNAL_KNOWLEDGE_APIS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."EXTERNAL_KNOWLEDGE_APIS_NAME_IDX" ON "DIFY"."EXTERNAL_KNOWLEDGE_APIS"("NAME" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."EXTERNAL_KNOWLEDGE_BINDINGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"EXTERNAL_KNOWLEDGE_API_ID" VARCHAR(50) NOT NULL,
"DATASET_ID" VARCHAR(50) NOT NULL,
"EXTERNAL_KNOWLEDGE_ID" TEXT NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_BY" VARCHAR(50),
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "EXTERNAL_KNOWLEDGE_BINDINGS_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."EXTERNAL_KNOWLEDGE_BINDINGS_DATASET_IDX" ON "DIFY"."EXTERNAL_KNOWLEDGE_BINDINGS"("DATASET_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."EXTERNAL_KNOWLEDGE_BINDINGS_EXTERNAL_KNOWLEDGE_API_IDX" ON "DIFY"."EXTERNAL_KNOWLEDGE_BINDINGS"("EXTERNAL_KNOWLEDGE_API_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."EXTERNAL_KNOWLEDGE_BINDINGS_TENANT_IDX" ON "DIFY"."EXTERNAL_KNOWLEDGE_BINDINGS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."INSTALLED_APPS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"APP_OWNER_TENANT_ID" VARCHAR(50) NOT NULL,
"POSITION" INT NOT NULL,
"IS_PINNED" BIT DEFAULT 0 NOT NULL,
"LAST_USED_AT" TIMESTAMP(6),
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "INSTALLED_APP_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_TENANT_APP" UNIQUE("APP_ID", "TENANT_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."INSTALLED_APP_TENANT_ID_IDX" ON "DIFY"."INSTALLED_APPS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."INSTALLED_APP_APP_ID_IDX" ON "DIFY"."INSTALLED_APPS"("APP_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."INVITATION_CODES"
(
"ID" INT DEFAULT "DIFY"."INVITATION_CODES_ID_SEQ".NEXTVAL NOT NULL,
"BATCH" VARCHAR(255 CHAR) NOT NULL,
"CODE" VARCHAR(32 CHAR) NOT NULL,
"STATUS" VARCHAR(16 CHAR) DEFAULT 'unused' NOT NULL,
"USED_AT" TIMESTAMP(6),
"USED_BY_TENANT_ID" VARCHAR(50),
"USED_BY_ACCOUNT_ID" VARCHAR(50),
"DEPRECATED_AT" TIMESTAMP(6),
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "INVITATION_CODE_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."INVITATION_CODES_CODE_IDX" ON "DIFY"."INVITATION_CODES"("CODE" ASC,"STATUS" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."INVITATION_CODES_BATCH_IDX" ON "DIFY"."INVITATION_CODES"("BATCH" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."LOAD_BALANCING_MODEL_CONFIGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"PROVIDER_NAME" VARCHAR(255 CHAR) NOT NULL,
"MODEL_NAME" VARCHAR(255 CHAR) NOT NULL,
"MODEL_TYPE" VARCHAR(40 CHAR) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"ENCRYPTED_CONFIG" TEXT,
"ENABLED" BIT DEFAULT 1 NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "LOAD_BALANCING_MODEL_CONFIG_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."LOAD_BALANCING_MODEL_CONFIG_TENANT_PROVIDER_MODEL_IDX" ON "DIFY"."LOAD_BALANCING_MODEL_CONFIGS"("MODEL_TYPE" ASC,"PROVIDER_NAME" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."MESSAGE_AGENT_THOUGHTS"
(
"ID" VARCHAR(50) NOT NULL,
"MESSAGE_ID" VARCHAR(50) NOT NULL,
"MESSAGE_CHAIN_ID" VARCHAR(50),
"POSITION" INT NOT NULL,
"THOUGHT" TEXT,
"TOOL" TEXT,
"TOOL_INPUT" TEXT,
"OBSERVATION" TEXT,
"TOOL_PROCESS_DATA" TEXT,
"MESSAGE" TEXT,
"MESSAGE_TOKEN" INT,
"MESSAGE_UNIT_PRICE" NUMERIC,
"ANSWER" TEXT,
"ANSWER_TOKEN" INT,
"ANSWER_UNIT_PRICE" NUMERIC,
"TOKENS" INT,
"TOTAL_PRICE" NUMERIC,
"CURRENCY" VARCHAR(32767 CHAR),
"LATENCY" DOUBLE,
"CREATED_BY_ROLE" VARCHAR(32767 CHAR) NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
"MESSAGE_PRICE_UNIT" NUMERIC(10,7) DEFAULT 0.001 NOT NULL,
"ANSWER_PRICE_UNIT" NUMERIC(10,7) DEFAULT 0.001 NOT NULL,
"MESSAGE_FILES" TEXT,
"TOOL_LABELS_STR" TEXT DEFAULT '{}' NOT NULL,
"TOOL_META_STR" TEXT DEFAULT '{}' NOT NULL,
CONSTRAINT "MESSAGE_AGENT_THOUGHT_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_AGENT_THOUGHT_MESSAGE_CHAIN_ID_IDX" ON "DIFY"."MESSAGE_AGENT_THOUGHTS"("MESSAGE_CHAIN_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_AGENT_THOUGHT_MESSAGE_ID_IDX" ON "DIFY"."MESSAGE_AGENT_THOUGHTS"("MESSAGE_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."MESSAGE_ANNOTATIONS"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"CONVERSATION_ID" VARCHAR(50),
"MESSAGE_ID" VARCHAR(50),
"CONTENT" TEXT NOT NULL,
"ACCOUNT_ID" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"QUESTION" TEXT,
"HIT_COUNT" INT DEFAULT 0 NOT NULL,
CONSTRAINT "MESSAGE_ANNOTATION_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_ANNOTATION_CONVERSATION_IDX" ON "DIFY"."MESSAGE_ANNOTATIONS"("CONVERSATION_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_ANNOTATION_MESSAGE_IDX" ON "DIFY"."MESSAGE_ANNOTATIONS"("MESSAGE_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_ANNOTATION_APP_IDX" ON "DIFY"."MESSAGE_ANNOTATIONS"("APP_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."MESSAGE_CHAINS"
(
"ID" VARCHAR(50) NOT NULL,
"MESSAGE_ID" VARCHAR(50) NOT NULL,
"TYPE" VARCHAR(255 CHAR) NOT NULL,
"INPUT" TEXT,
"OUTPUT" TEXT,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
CONSTRAINT "MESSAGE_CHAIN_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_CHAIN_MESSAGE_ID_IDX" ON "DIFY"."MESSAGE_CHAINS"("MESSAGE_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."MESSAGE_FEEDBACKS"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"CONVERSATION_ID" VARCHAR(50) NOT NULL,
"MESSAGE_ID" VARCHAR(50) NOT NULL,
"RATING" VARCHAR(255 CHAR) NOT NULL,
"CONTENT" TEXT,
"FROM_SOURCE" VARCHAR(255 CHAR) NOT NULL,
"FROM_END_USER_ID" VARCHAR(50),
"FROM_ACCOUNT_ID" VARCHAR(50),
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "MESSAGE_FEEDBACK_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_FEEDBACK_CONVERSATION_IDX" ON "DIFY"."MESSAGE_FEEDBACKS"("CONVERSATION_ID" ASC,"FROM_SOURCE" ASC,"RATING" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_FEEDBACK_MESSAGE_IDX" ON "DIFY"."MESSAGE_FEEDBACKS"("FROM_SOURCE" ASC,"MESSAGE_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_FEEDBACK_APP_IDX" ON "DIFY"."MESSAGE_FEEDBACKS"("APP_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."MESSAGE_FILES"
(
"ID" VARCHAR(50) NOT NULL,
"MESSAGE_ID" VARCHAR(50) NOT NULL,
"TYPE" VARCHAR(255 CHAR) NOT NULL,
"TRANSFER_METHOD" VARCHAR(255 CHAR) NOT NULL,
"URL" TEXT,
"UPLOAD_FILE_ID" VARCHAR(50),
"CREATED_BY_ROLE" VARCHAR(255 CHAR) NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"BELONGS_TO" VARCHAR(255 CHAR),
CONSTRAINT "MESSAGE_FILE_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_FILE_MESSAGE_IDX" ON "DIFY"."MESSAGE_FILES"("MESSAGE_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_FILE_CREATED_BY_IDX" ON "DIFY"."MESSAGE_FILES"("CREATED_BY" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."MESSAGES"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"MODEL_PROVIDER" VARCHAR(255 CHAR),
"MODEL_ID" VARCHAR(255 CHAR),
"OVERRIDE_MODEL_CONFIGS" TEXT,
"CONVERSATION_ID" VARCHAR(50) NOT NULL,
"INPUTS" VARCHAR(32767) NOT NULL,
"QUERY" TEXT NOT NULL,
"MESSAGE" VARCHAR(32767) NOT NULL,
"MESSAGE_TOKENS" INT DEFAULT 0 NOT NULL,
"MESSAGE_UNIT_PRICE" NUMERIC(10,4) NOT NULL,
"ANSWER" TEXT NOT NULL,
"ANSWER_TOKENS" INT DEFAULT 0 NOT NULL,
"ANSWER_UNIT_PRICE" NUMERIC(10,4) NOT NULL,
"PROVIDER_RESPONSE_LATENCY" DOUBLE DEFAULT 0 NOT NULL,
"TOTAL_PRICE" NUMERIC(10,7),
"CURRENCY" VARCHAR(255 CHAR) NOT NULL,
"FROM_SOURCE" VARCHAR(255 CHAR) NOT NULL,
"FROM_END_USER_ID" VARCHAR(50),
"FROM_ACCOUNT_ID" VARCHAR(50),
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"AGENT_BASED" BIT DEFAULT 0 NOT NULL,
"MESSAGE_PRICE_UNIT" NUMERIC(10,7) DEFAULT 0.001 NOT NULL,
"ANSWER_PRICE_UNIT" NUMERIC(10,7) DEFAULT 0.001 NOT NULL,
"WORKFLOW_RUN_ID" VARCHAR(50),
"STATUS" VARCHAR(255 CHAR) DEFAULT 'normal' NOT NULL,
"ERROR" TEXT,
"MESSAGE_METADATA" TEXT,
"INVOKE_FROM" VARCHAR(255 CHAR),
"PARENT_MESSAGE_ID" VARCHAR(50),
CONSTRAINT "MESSAGE_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_WORKFLOW_RUN_ID_IDX" ON "DIFY"."MESSAGES"("CONVERSATION_ID" ASC,"WORKFLOW_RUN_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_CONVERSATION_ID_IDX" ON "DIFY"."MESSAGES"("CONVERSATION_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_ACCOUNT_IDX" ON "DIFY"."MESSAGES"("APP_ID" ASC,"FROM_ACCOUNT_ID" ASC,"FROM_SOURCE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_APP_ID_IDX" ON "DIFY"."MESSAGES"("APP_ID" ASC,"CREATED_AT" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_CREATED_AT_IDX" ON "DIFY"."MESSAGES"("CREATED_AT" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."MESSAGE_END_USER_IDX" ON "DIFY"."MESSAGES"("APP_ID" ASC,"FROM_END_USER_ID" ASC,"FROM_SOURCE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."OPERATION_LOGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"ACCOUNT_ID" VARCHAR(50) NOT NULL,
"ACTION" VARCHAR(255 CHAR) NOT NULL,
"CONTENT" VARCHAR(32767),
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"CREATED_IP" VARCHAR(255 CHAR) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "OPERATION_LOG_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."OPERATION_LOG_ACCOUNT_ACTION_IDX" ON "DIFY"."OPERATION_LOGS"("ACCOUNT_ID" ASC,"ACTION" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."PINNED_CONVERSATIONS"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"CONVERSATION_ID" VARCHAR(50) NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"CREATED_BY_ROLE" VARCHAR(255 CHAR) DEFAULT 'end_user' NOT NULL,
CONSTRAINT "PINNED_CONVERSATION_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."PINNED_CONVERSATION_CONVERSATION_IDX" ON "DIFY"."PINNED_CONVERSATIONS"("APP_ID" ASC,"CONVERSATION_ID" ASC,"CREATED_BY" ASC,"CREATED_BY_ROLE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."PROVIDER_MODEL_SETTINGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"PROVIDER_NAME" VARCHAR(255 CHAR) NOT NULL,
"MODEL_NAME" VARCHAR(255 CHAR) NOT NULL,
"MODEL_TYPE" VARCHAR(40 CHAR) NOT NULL,
"ENABLED" BIT DEFAULT 1 NOT NULL,
"LOAD_BALANCING_ENABLED" BIT DEFAULT 0 NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "PROVIDER_MODEL_SETTING_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."PROVIDER_MODEL_SETTING_TENANT_PROVIDER_MODEL_IDX" ON "DIFY"."PROVIDER_MODEL_SETTINGS"("MODEL_TYPE" ASC,"PROVIDER_NAME" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."PROVIDER_MODELS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"PROVIDER_NAME" VARCHAR(255 CHAR) NOT NULL,
"MODEL_NAME" VARCHAR(255 CHAR) NOT NULL,
"MODEL_TYPE" VARCHAR(40 CHAR) NOT NULL,
"ENCRYPTED_CONFIG" TEXT,
"IS_VALID" BIT DEFAULT 0 NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "PROVIDER_MODEL_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_PROVIDER_MODEL_NAME" UNIQUE("MODEL_NAME", "MODEL_TYPE", "PROVIDER_NAME", "TENANT_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."PROVIDER_MODEL_TENANT_ID_PROVIDER_IDX" ON "DIFY"."PROVIDER_MODELS"("PROVIDER_NAME" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."PROVIDER_ORDERS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"PROVIDER_NAME" VARCHAR(255 CHAR) NOT NULL,
"ACCOUNT_ID" VARCHAR(50) NOT NULL,
"PAYMENT_PRODUCT_ID" VARCHAR(191 CHAR) NOT NULL,
"PAYMENT_ID" VARCHAR(191 CHAR),
"TRANSACTION_ID" VARCHAR(191 CHAR),
"QUANTITY" INT DEFAULT 1 NOT NULL,
"CURRENCY" VARCHAR(40 CHAR),
"TOTAL_AMOUNT" INT,
"PAYMENT_STATUS" VARCHAR(40 CHAR) DEFAULT 'wait_pay' NOT NULL,
"PAID_AT" TIMESTAMP(6),
"PAY_FAILED_AT" TIMESTAMP(6),
"REFUNDED_AT" TIMESTAMP(6),
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "PROVIDER_ORDER_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."PROVIDER_ORDER_TENANT_PROVIDER_IDX" ON "DIFY"."PROVIDER_ORDERS"("PROVIDER_NAME" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."PROVIDERS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"PROVIDER_NAME" VARCHAR(255 CHAR) NOT NULL,
"PROVIDER_TYPE" VARCHAR(40 CHAR) DEFAULT 'custom' NOT NULL,
"ENCRYPTED_CONFIG" TEXT,
"IS_VALID" BIT DEFAULT 0 NOT NULL,
"LAST_USED" TIMESTAMP(6),
"QUOTA_TYPE" VARCHAR(40 CHAR) DEFAULT '',
"QUOTA_LIMIT" BIGINT,
"QUOTA_USED" BIGINT,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "PROVIDER_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_PROVIDER_NAME_TYPE_QUOTA" UNIQUE("PROVIDER_NAME", "PROVIDER_TYPE", "QUOTA_TYPE", "TENANT_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."PROVIDER_TENANT_ID_PROVIDER_IDX" ON "DIFY"."PROVIDERS"("PROVIDER_NAME" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."RATE_LIMIT_LOGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"SUBSCRIPTION_PLAN" VARCHAR(255 CHAR) NOT NULL,
"OPERATION" VARCHAR(255 CHAR) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "RATE_LIMIT_LOG_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."RATE_LIMIT_LOG_OPERATION_IDX" ON "DIFY"."RATE_LIMIT_LOGS"("OPERATION" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."RATE_LIMIT_LOG_TENANT_IDX" ON "DIFY"."RATE_LIMIT_LOGS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."RECOMMENDED_APPS"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"DESCRIPTION" VARCHAR(32767) NOT NULL,
"COPYRIGHT" VARCHAR(255 CHAR) NOT NULL,
"PRIVACY_POLICY" VARCHAR(255 CHAR) NOT NULL,
"CATEGORY" VARCHAR(255 CHAR) NOT NULL,
"POSITION" INT NOT NULL,
"IS_LISTED" BIT NOT NULL,
"INSTALL_COUNT" INT NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"LANGUAGE" VARCHAR(255 CHAR) DEFAULT 'en-US' NOT NULL,
"CUSTOM_DISCLAIMER" TEXT NOT NULL,
CONSTRAINT "RECOMMENDED_APP_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."RECOMMENDED_APP_IS_LISTED_IDX" ON "DIFY"."RECOMMENDED_APPS"("IS_LISTED" ASC,"LANGUAGE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."RECOMMENDED_APP_APP_ID_IDX" ON "DIFY"."RECOMMENDED_APPS"("APP_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."SAVED_MESSAGES"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"MESSAGE_ID" VARCHAR(50) NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"CREATED_BY_ROLE" VARCHAR(255 CHAR) DEFAULT 'end_user' NOT NULL,
CONSTRAINT "SAVED_MESSAGE_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."SAVED_MESSAGE_MESSAGE_IDX" ON "DIFY"."SAVED_MESSAGES"("APP_ID" ASC,"CREATED_BY" ASC,"CREATED_BY_ROLE" ASC,"MESSAGE_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."SITES"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"TITLE" VARCHAR(255 CHAR) NOT NULL,
"ICON" VARCHAR(255 CHAR),
"ICON_BACKGROUND" VARCHAR(255 CHAR),
"DESCRIPTION" TEXT,
"DEFAULT_LANGUAGE" VARCHAR(255 CHAR) NOT NULL,
"COPYRIGHT" VARCHAR(255 CHAR),
"PRIVACY_POLICY" VARCHAR(255 CHAR),
"CUSTOMIZE_DOMAIN" VARCHAR(255 CHAR),
"CUSTOMIZE_TOKEN_STRATEGY" VARCHAR(255 CHAR) NOT NULL,
"PROMPT_PUBLIC" BIT DEFAULT 0 NOT NULL,
"STATUS" VARCHAR(255 CHAR) DEFAULT 'normal' NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"CODE" VARCHAR(255 CHAR),
"CUSTOM_DISCLAIMER" TEXT NOT NULL,
"SHOW_WORKFLOW_STEPS" BIT DEFAULT 1 NOT NULL,
"CHAT_COLOR_THEME" VARCHAR(255 CHAR),
"CHAT_COLOR_THEME_INVERTED" BIT DEFAULT 0 NOT NULL,
"ICON_TYPE" VARCHAR(255 CHAR),
"CREATED_BY" VARCHAR(50),
"UPDATED_BY" VARCHAR(50),
"USE_ICON_AS_ANSWER_ICON" BIT DEFAULT 0 NOT NULL,
CONSTRAINT "SITE_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."SITE_CODE_IDX" ON "DIFY"."SITES"("CODE" ASC,"STATUS" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."SITE_APP_ID_IDX" ON "DIFY"."SITES"("APP_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TAG_BINDINGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50),
"TAG_ID" VARCHAR(50),
"TARGET_ID" VARCHAR(50),
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "TAG_BINDING_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TAG_BIND_TAG_ID_IDX" ON "DIFY"."TAG_BINDINGS"("TAG_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TAG_BIND_TARGET_ID_IDX" ON "DIFY"."TAG_BINDINGS"("TARGET_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TAGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50),
"TYPE" VARCHAR(16 CHAR) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "TAG_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TAG_NAME_IDX" ON "DIFY"."TAGS"("NAME" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TAG_TYPE_IDX" ON "DIFY"."TAGS"("TYPE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TENANT_ACCOUNT_JOINS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"ACCOUNT_ID" VARCHAR(50) NOT NULL,
"ROLE" VARCHAR(16 CHAR) DEFAULT 'normal' NOT NULL,
"INVITED_BY" VARCHAR(50),
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"CURRENT" BIT DEFAULT 0 NOT NULL,
CONSTRAINT "TENANT_ACCOUNT_JOIN_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_TENANT_ACCOUNT_JOIN" UNIQUE("ACCOUNT_ID", "TENANT_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TENANT_ACCOUNT_JOIN_ACCOUNT_ID_IDX" ON "DIFY"."TENANT_ACCOUNT_JOINS"("ACCOUNT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TENANT_ACCOUNT_JOIN_TENANT_ID_IDX" ON "DIFY"."TENANT_ACCOUNT_JOINS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TENANT_DEFAULT_MODELS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"PROVIDER_NAME" VARCHAR(255 CHAR) NOT NULL,
"MODEL_NAME" VARCHAR(255 CHAR) NOT NULL,
"MODEL_TYPE" VARCHAR(40 CHAR) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "TENANT_DEFAULT_MODEL_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TENANT_DEFAULT_MODEL_TENANT_ID_PROVIDER_TYPE_IDX" ON "DIFY"."TENANT_DEFAULT_MODELS"("MODEL_TYPE" ASC,"PROVIDER_NAME" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TENANT_PREFERRED_MODEL_PROVIDERS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"PROVIDER_NAME" VARCHAR(255 CHAR) NOT NULL,
"PREFERRED_PROVIDER_TYPE" VARCHAR(40 CHAR) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "TENANT_PREFERRED_MODEL_PROVIDER_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TENANT_PREFERRED_MODEL_PROVIDER_TENANT_PROVIDER_IDX" ON "DIFY"."TENANT_PREFERRED_MODEL_PROVIDERS"("PROVIDER_NAME" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TENANTS"
(
"ID" VARCHAR(50) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"ENCRYPT_PUBLIC_KEY" TEXT,
"PLAN" VARCHAR(255 CHAR) DEFAULT 'basic' NOT NULL,
"STATUS" VARCHAR(255 CHAR) DEFAULT 'normal' NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"CUSTOM_CONFIG" TEXT,
CONSTRAINT "TENANT_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TIDB_AUTH_BINDINGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50),
"CLUSTER_ID" VARCHAR(255 CHAR) NOT NULL,
"CLUSTER_NAME" VARCHAR(255 CHAR) NOT NULL,
"ACTIVE" BIT DEFAULT 0 NOT NULL,
"STATUS" VARCHAR(255 CHAR) DEFAULT 'CREATING' NOT NULL,
"ACCOUNT" VARCHAR(255 CHAR) NOT NULL,
"PASSWORD" VARCHAR(255 CHAR) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "TIDB_AUTH_BINDINGS_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TIDB_AUTH_BINDINGS_ACTIVE_IDX" ON "DIFY"."TIDB_AUTH_BINDINGS"("ACTIVE" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TIDB_AUTH_BINDINGS_CREATED_AT_IDX" ON "DIFY"."TIDB_AUTH_BINDINGS"("CREATED_AT" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TIDB_AUTH_BINDINGS_TENANT_IDX" ON "DIFY"."TIDB_AUTH_BINDINGS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TIDB_AUTH_BINDINGS_STATUS_IDX" ON "DIFY"."TIDB_AUTH_BINDINGS"("STATUS" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TOOL_API_PROVIDERS"
(
"ID" VARCHAR(50) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"SCHEMA" TEXT NOT NULL,
"SCHEMA_TYPE_STR" VARCHAR(40 CHAR) NOT NULL,
"USER_ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"TOOLS_STR" TEXT NOT NULL,
"ICON" VARCHAR(255 CHAR) NOT NULL,
"CREDENTIALS_STR" TEXT NOT NULL,
"DESCRIPTION" TEXT NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"PRIVACY_POLICY" VARCHAR(255 CHAR),
"CUSTOM_DISCLAIMER" TEXT NOT NULL,
CONSTRAINT "TOOL_API_PROVIDER_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_API_TOOL_PROVIDER" UNIQUE("NAME", "TENANT_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TOOL_BUILTIN_PROVIDERS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50),
"USER_ID" VARCHAR(50) NOT NULL,
"PROVIDER" VARCHAR(256 CHAR) NOT NULL,
"ENCRYPTED_CREDENTIALS" TEXT,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "TOOL_BUILTIN_PROVIDER_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_BUILTIN_TOOL_PROVIDER" UNIQUE("PROVIDER", "TENANT_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TOOL_CONVERSATION_VARIABLES"
(
"ID" VARCHAR(50) NOT NULL,
"USER_ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"CONVERSATION_ID" VARCHAR(50) NOT NULL,
"VARIABLES_STR" TEXT NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "TOOL_CONVERSATION_VARIABLES_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."CONVERSATION_ID_IDX" ON "DIFY"."TOOL_CONVERSATION_VARIABLES"("CONVERSATION_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."USER_ID_IDX" ON "DIFY"."TOOL_CONVERSATION_VARIABLES"("USER_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TOOL_FILES"
(
"ID" VARCHAR(50) NOT NULL,
"USER_ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"CONVERSATION_ID" VARCHAR(50),
"FILE_KEY" VARCHAR(255 CHAR) NOT NULL,
"MIMETYPE" VARCHAR(255 CHAR) NOT NULL,
"ORIGINAL_URL" VARCHAR(2048 CHAR),
"NAME" VARCHAR(32767 CHAR) NOT NULL,
"SIZE" INT NOT NULL,
CONSTRAINT "TOOL_FILE_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TOOL_FILE_CONVERSATION_ID_IDX" ON "DIFY"."TOOL_FILES"("CONVERSATION_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TOOL_LABEL_BINDINGS"
(
"ID" VARCHAR(50) NOT NULL,
"TOOL_ID" VARCHAR(64 CHAR) NOT NULL,
"TOOL_TYPE" VARCHAR(40 CHAR) NOT NULL,
"LABEL_NAME" VARCHAR(40 CHAR) NOT NULL,
CONSTRAINT "TOOL_LABEL_BIND_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_TOOL_LABEL_BIND" UNIQUE("LABEL_NAME", "TOOL_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TOOL_MCP_PROVIDERS"
(
"ID" VARCHAR(50) NOT NULL,
"NAME" VARCHAR(40 CHAR) NOT NULL,
"SERVER_IDENTIFIER" VARCHAR(24 CHAR) NOT NULL,
"SERVER_URL" TEXT NOT NULL,
"SERVER_URL_HASH" VARCHAR(64 CHAR) NOT NULL,
"ICON" VARCHAR(255 CHAR),
"TENANT_ID" VARCHAR(50) NOT NULL,
"USER_ID" VARCHAR(50) NOT NULL,
"ENCRYPTED_CREDENTIALS" TEXT,
"AUTHED" BIT NOT NULL,
"TOOLS" TEXT NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "TOOL_MCP_PROVIDER_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_MCP_PROVIDER_SERVER_IDENTIFIER" UNIQUE("SERVER_IDENTIFIER", "TENANT_ID"),
CONSTRAINT "UNIQUE_MCP_PROVIDER_SERVER_URL" UNIQUE("SERVER_URL_HASH", "TENANT_ID"),
CONSTRAINT "UNIQUE_MCP_PROVIDER_NAME" UNIQUE("NAME", "TENANT_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TOOL_MODEL_INVOKES"
(
"ID" VARCHAR(50) NOT NULL,
"USER_ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"PROVIDER" VARCHAR(255 CHAR) NOT NULL,
"TOOL_TYPE" VARCHAR(40 CHAR) NOT NULL,
"TOOL_NAME" VARCHAR(40 CHAR) NOT NULL,
"MODEL_PARAMETERS" TEXT NOT NULL,
"PROMPT_MESSAGES" TEXT NOT NULL,
"MODEL_RESPONSE" TEXT NOT NULL,
"PROMPT_TOKENS" INT DEFAULT 0 NOT NULL,
"ANSWER_TOKENS" INT DEFAULT 0 NOT NULL,
"ANSWER_UNIT_PRICE" NUMERIC(10,4) NOT NULL,
"ANSWER_PRICE_UNIT" NUMERIC(10,7) DEFAULT 0.001 NOT NULL,
"PROVIDER_RESPONSE_LATENCY" DOUBLE DEFAULT 0 NOT NULL,
"TOTAL_PRICE" NUMERIC(10,7),
"CURRENCY" VARCHAR(255 CHAR) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "TOOL_MODEL_INVOKE_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TOOL_PUBLISHED_APPS"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"USER_ID" VARCHAR(50) NOT NULL,
"DESCRIPTION" TEXT NOT NULL,
"LLM_DESCRIPTION" TEXT NOT NULL,
"QUERY_DESCRIPTION" TEXT NOT NULL,
"QUERY_NAME" VARCHAR(40 CHAR) NOT NULL,
"TOOL_NAME" VARCHAR(40 CHAR) NOT NULL,
"AUTHOR" VARCHAR(40 CHAR) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "PUBLISHED_APP_TOOL_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_PUBLISHED_APP_TOOL" UNIQUE("APP_ID", "USER_ID"),
CONSTRAINT "TOOL_PUBLISHED_APPS_APP_ID_FKEY" FOREIGN KEY("APP_ID") REFERENCES "DIFY"."APPS"("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TOOL_WORKFLOW_PROVIDERS"
(
"ID" VARCHAR(50) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"ICON" VARCHAR(255 CHAR) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"USER_ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"DESCRIPTION" TEXT NOT NULL,
"PARAMETER_CONFIGURATION" TEXT DEFAULT '[]' NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"PRIVACY_POLICY" VARCHAR(255 CHAR) DEFAULT '',
"VERSION" VARCHAR(255 CHAR) DEFAULT '' NOT NULL,
"LABEL" VARCHAR(255 CHAR) DEFAULT '' NOT NULL,
CONSTRAINT "TOOL_WORKFLOW_PROVIDER_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UNIQUE_WORKFLOW_TOOL_PROVIDER_APP_ID" UNIQUE("APP_ID", "TENANT_ID"),
CONSTRAINT "UNIQUE_WORKFLOW_TOOL_PROVIDER" UNIQUE("NAME", "TENANT_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."TRACE_APP_CONFIG"
(
"ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"TRACING_PROVIDER" VARCHAR(255 CHAR),
"TRACING_CONFIG" VARCHAR(32767),
"CREATED_AT" TIMESTAMP(6) DEFAULT NOW() NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT NOW() NOT NULL,
"IS_ACTIVE" BIT DEFAULT 1 NOT NULL,
CONSTRAINT "TRACE_APP_CONFIG_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."TRACE_APP_CONFIG_APP_ID_IDX" ON "DIFY"."TRACE_APP_CONFIG"("APP_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."UPLOAD_FILES"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"STORAGE_TYPE" VARCHAR(255 CHAR) NOT NULL,
"KEY" VARCHAR(255 CHAR) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"SIZE" INT NOT NULL,
"EXTENSION" VARCHAR(255 CHAR) NOT NULL,
"MIME_TYPE" VARCHAR(255 CHAR),
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"USED" BIT DEFAULT 0 NOT NULL,
"USED_BY" VARCHAR(50),
"USED_AT" TIMESTAMP(6),
"HASH" VARCHAR(255 CHAR),
"CREATED_BY_ROLE" VARCHAR(255 CHAR) DEFAULT 'account' NOT NULL,
"SOURCE_URL" TEXT DEFAULT '' NOT NULL,
CONSTRAINT "UPLOAD_FILE_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."UPLOAD_FILE_TENANT_IDX" ON "DIFY"."UPLOAD_FILES"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."WHITELISTS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50),
"CATEGORY" VARCHAR(255 CHAR) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "WHITELISTS_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."WHITELISTS_TENANT_IDX" ON "DIFY"."WHITELISTS"("TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."WORKFLOW_APP_LOGS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"WORKFLOW_ID" VARCHAR(50) NOT NULL,
"WORKFLOW_RUN_ID" VARCHAR(50) NOT NULL,
"CREATED_FROM" VARCHAR(255 CHAR) NOT NULL,
"CREATED_BY_ROLE" VARCHAR(255 CHAR) NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
CONSTRAINT "WORKFLOW_APP_LOG_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."WORKFLOW_APP_LOG_APP_IDX" ON "DIFY"."WORKFLOW_APP_LOGS"("APP_ID" ASC,"TENANT_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."WORKFLOW_CONVERSATION_VARIABLES"
(
"ID" VARCHAR(50) NOT NULL,
"CONVERSATION_ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"DATA" TEXT NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
CONSTRAINT "WORKFLOW__CONVERSATION_VARIABLES_PKEY" NOT CLUSTER PRIMARY KEY("CONVERSATION_ID", "ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."WORKFLOW_CONVERSATION_VARIABLES_CONVERSATION_ID_IDX" ON "DIFY"."WORKFLOW_CONVERSATION_VARIABLES"("CONVERSATION_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."WORKFLOW_CONVERSATION_VARIABLES_CREATED_AT_IDX" ON "DIFY"."WORKFLOW_CONVERSATION_VARIABLES"("CREATED_AT" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."WORKFLOW_CONVERSATION_VARIABLES_APP_ID_IDX" ON "DIFY"."WORKFLOW_CONVERSATION_VARIABLES"("APP_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."WORKFLOW_DRAFT_VARIABLES"
(
"ID" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
"UPDATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"LAST_EDITED_AT" TIMESTAMP(6),
"NODE_ID" VARCHAR(255 CHAR) NOT NULL,
"NAME" VARCHAR(255 CHAR) NOT NULL,
"DESCRIPTION" VARCHAR(255 CHAR) NOT NULL,
"SELECTOR" VARCHAR(255 CHAR) NOT NULL,
"VALUE_TYPE" VARCHAR(20 CHAR) NOT NULL,
"VALUE" TEXT NOT NULL,
"VISIBLE" BIT NOT NULL,
"EDITABLE" BIT NOT NULL,
"NODE_EXECUTION_ID" VARCHAR(50),
CONSTRAINT "WORKFLOW_DRAFT_VARIABLES_PKEY" NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "WORKFLOW_DRAFT_VARIABLES_APP_ID_KEY" UNIQUE("APP_ID", "NAME", "NODE_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."WORKFLOW_NODE_EXECUTIONS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"WORKFLOW_ID" VARCHAR(50) NOT NULL,
"TRIGGERED_FROM" VARCHAR(255 CHAR) NOT NULL,
"WORKFLOW_RUN_ID" VARCHAR(50),
"INDEX" INT NOT NULL,
"PREDECESSOR_NODE_ID" VARCHAR(255 CHAR),
"NODE_ID" VARCHAR(255 CHAR) NOT NULL,
"NODE_TYPE" VARCHAR(255 CHAR) NOT NULL,
"TITLE" VARCHAR(255 CHAR) NOT NULL,
"INPUTS" TEXT,
"PROCESS_DATA" TEXT,
"OUTPUTS" TEXT,
"STATUS" VARCHAR(255 CHAR) NOT NULL,
"ERROR" TEXT,
"ELAPSED_TIME" DOUBLE DEFAULT 0 NOT NULL,
"EXECUTION_METADATA" TEXT,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"CREATED_BY_ROLE" VARCHAR(255 CHAR) NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"FINISHED_AT" TIMESTAMP(6),
"NODE_EXECUTION_ID" VARCHAR(255 CHAR),
CONSTRAINT "WORKFLOW_NODE_EXECUTION_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."WORKFLOW_NODE_EXECUTION_WORKFLOW_RUN_IDX" ON "DIFY"."WORKFLOW_NODE_EXECUTIONS"("APP_ID" ASC,"TENANT_ID" ASC,"TRIGGERED_FROM" ASC,"WORKFLOW_ID" ASC,"WORKFLOW_RUN_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."WORKFLOW_NODE_EXECUTIONS_TENANT_ID_IDX" ON "DIFY"."WORKFLOW_NODE_EXECUTIONS"("CREATED_AT" ASC,"NODE_ID" ASC,"TENANT_ID" ASC,"WORKFLOW_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."WORKFLOW_NODE_EXECUTION_ID_IDX" ON "DIFY"."WORKFLOW_NODE_EXECUTIONS"("APP_ID" ASC,"NODE_EXECUTION_ID" ASC,"TENANT_ID" ASC,"TRIGGERED_FROM" ASC,"WORKFLOW_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."WORKFLOW_NODE_EXECUTION_NODE_RUN_IDX" ON "DIFY"."WORKFLOW_NODE_EXECUTIONS"("APP_ID" ASC,"NODE_ID" ASC,"TENANT_ID" ASC,"TRIGGERED_FROM" ASC,"WORKFLOW_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."WORKFLOW_RUNS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"WORKFLOW_ID" VARCHAR(50) NOT NULL,
"TYPE" VARCHAR(255 CHAR) NOT NULL,
"TRIGGERED_FROM" VARCHAR(255 CHAR) NOT NULL,
"VERSION" VARCHAR(255 CHAR) NOT NULL,
"GRAPH" TEXT,
"INPUTS" TEXT,
"STATUS" VARCHAR(255 CHAR) NOT NULL,
"OUTPUTS" TEXT,
"ERROR" TEXT,
"ELAPSED_TIME" DOUBLE DEFAULT 0 NOT NULL,
"TOTAL_TOKENS" BIGINT DEFAULT 0 NOT NULL,
"TOTAL_STEPS" INT DEFAULT 0,
"CREATED_BY_ROLE" VARCHAR(255 CHAR) NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"FINISHED_AT" TIMESTAMP(6),
"EXCEPTIONS_COUNT" INT DEFAULT 0,
CONSTRAINT "WORKFLOW_RUN_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."WORKFLOW_RUN_TRIGGERD_FROM_IDX" ON "DIFY"."WORKFLOW_RUNS"("APP_ID" ASC,"TENANT_ID" ASC,"TRIGGERED_FROM" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "DIFY"."WORKFLOWS"
(
"ID" VARCHAR(50) NOT NULL,
"TENANT_ID" VARCHAR(50) NOT NULL,
"APP_ID" VARCHAR(50) NOT NULL,
"TYPE" VARCHAR(255 CHAR) NOT NULL,
"VERSION" VARCHAR(255 CHAR) NOT NULL,
"GRAPH" TEXT NOT NULL,
"FEATURES" TEXT NOT NULL,
"CREATED_BY" VARCHAR(50) NOT NULL,
"CREATED_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(0) NOT NULL,
"UPDATED_BY" VARCHAR(50),
"UPDATED_AT" TIMESTAMP(6) NOT NULL,
"ENVIRONMENT_VARIABLES" TEXT DEFAULT '{}' NOT NULL,
"CONVERSATION_VARIABLES" TEXT DEFAULT '{}' NOT NULL,
"MARKED_NAME" VARCHAR(32767 CHAR) DEFAULT '' NOT NULL,
"MARKED_COMMENT" VARCHAR(32767 CHAR) DEFAULT '' NOT NULL,
CONSTRAINT "WORKFLOW_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE OR REPLACE  INDEX "DIFY"."WORKFLOW_VERSION_IDX" ON "DIFY"."WORKFLOWS"("APP_ID" ASC,"TENANT_ID" ASC,"VERSION" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


-- =====================================================
-- ACCOUNTS表新增字段注释说明
-- =====================================================

-- 为ACCOUNTS表新增字段添加注释
COMMENT ON COLUMN "DIFY"."ACCOUNTS"."USER_ID" IS '客户端自定义用户ID，用于外部系统集成';
COMMENT ON COLUMN "DIFY"."ACCOUNTS"."USER_TYPE" IS '用户类型：EMPLOYEE(内部用户)、TEMP(临时用户)';
COMMENT ON COLUMN "DIFY"."ACCOUNTS"."START_DATE" IS '临时用户生效时间，格式YYYY-MM-DD';
COMMENT ON COLUMN "DIFY"."ACCOUNTS"."END_DATE" IS '临时用户失效时间，格式YYYY-MM-DD';

-- 字段说明：
-- USER_ID: 用于存储客户端系统传入的自定义用户标识符，支持与外部系统集成
-- USER_TYPE: 区分内部用户和临时用户，默认为EMPLOYEE(内部用户)
-- START_DATE: 临时用户的生效日期，仅对USER_TYPE='TEMP'的用户有效
-- END_DATE: 临时用户的失效日期，仅对USER_TYPE='TEMP'的用户有效
--
-- 约束说明：
-- USER_ID字段添加了唯一约束，确保每个客户端用户ID在系统中唯一
-- USER_TYPE字段默认值为'EMPLOYEE'，符合大多数场景下的内部用户需求


