
好的，遵照您的指示，这里是基于之前确定的服务器分配方案的详细配置与部署步骤。

本方案将指导您完成从环境准备到最终在 Docker Swarm 集群上成功部署 Dify 应用的全过程。

---

### Phase 1: 环境准备 (在所有相关服务器上操作)

在开始之前，请确保您已在以下 **6台** 服务器上完成了这些准备工作：
*   `**************` (Manager/Worker)
*   `**************` (Manager/Worker)
*   `**************` (Worker)
*   `**************` (Worker)
*   `**************` (Worker)
*   `**************` (Worker)

**1. 安装 Docker:**
如果服务器上尚未安装 Docker，请使用以下命令进行安装：
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
# 启动 Docker 并设置为开机自启
sudo systemctl start docker
sudo systemctl enable docker
```

**2. 配置防火墙:**
Docker Swarm 集群内的节点间需要通过特定端口通信。请确保在所有 6 台服务器的防火墙（如 firewalld, ufw）上放开以下端口：
*   **TCP port 2377:** 用于集群管理通信。
*   **TCP and UDP port 7946:** 用于节点间通信。
*   **UDP port 4789:** 用于 overlay 网络流量。

另外，在 **两台 Manager 节点** (`**************`, `**************`) 上，需要对外开放 Nginx 服务的端口。根据您的 `.env.example` 文件，这个端口由 `EXPOSE_NGINX_PORT` 定义，默认为 `7001`。
```bash
# 示例：使用 firewalld 开放端口 (在所有6台节点上执行)
sudo firewall-cmd --add-port=2377/tcp --permanent
sudo firewall-cmd --add-port=7946/tcp --permanent
sudo firewall-cmd --add-port=7946/udp --permanent
sudo firewall-cmd --add-port=4789/udp --permanent

# 示例：仅在两台 Manager 节点上额外开放 Nginx 端口
sudo firewall-cmd --add-port=7001/tcp --permanent

# 重载防火墙使配置生效
sudo firewall-cmd --reload
```

---

### Phase 2: Docker Swarm 集群初始化与配置

**1. 初始化 Swarm 管理节点 (在 `**************` 上执行):**
```bash
# --advertise-addr 指定该节点在集群中的通信地址
docker swarm init --advertise-addr **************
```
执行后，系统会生成一个加入命令，类似 `docker swarm join --token <TOKEN> **************:2377`。**请先不要执行这个命令**。

**2. 添加第二个管理节点 (在 `**************` 上执行):**
首先，在主管理节点 (`**************`) 上获取**管理节点**的加入令牌：
```bash
# 在 ************** 上执行
docker swarm join-token manager
```
复制输出的 `docker swarm join ...` 命令，然后在**第二台 Manager 服务器 (`**************`)** 上执行它。

**3. 添加工作节点 (在其他4台 Worker 节点上执行):**
回到主管理节点 (`**************`)，获取**工作节点**的加入令牌：
```bash
# 在 ************** 上执行
docker swarm join-token worker
```
复制输出的 `docker swarm join ...` 命令，并分别在以下 **4台 Worker 服务器**上执行：
*   `**************`
*   `**************`
*   `**************`
*   `**************`

**4. 为专用节点打标签 (在 `**************` 上执行):**
为了将 Redis 和 Qdrant 固定到指定的服务器，我们需要为这些节点打上标签。
```bash
# 在 ************** 上执行
# 获取所有节点的 ID 和 Hostname
docker node ls

# 假设通过 docker node ls 查到 ************** 的节点ID为 'xxxx'
# 假设通过 docker node ls 查到 ************** 的节点ID为 'yyyy'
# (请替换 xxxx 和 yyyy 为实际的节点 ID)

# 为 Redis 节点打标签
docker node update --label-add role=redis-node xxxx

# 为 Qdrant 节点打标签
docker node update --label-add role=qdrant-node yyyy
```

**5. 验证集群状态 (在 `**************` 上执行):**
```bash
docker node ls
```
您应该能看到 6 个节点，其中 2 个是 `Manager` (一个是 Leader)，4 个是 `Worker`，状态都应为 `Ready`。

---

### Phase 3: 准备部署配置文件

在主管理节点 `**************` 上，创建一个工作目录，并将您的 `docker-compose.yaml` 和 `.env.example` 文件放入其中。

```bash
mkdir -p /data/dify-swarm
cd /data/dify-swarm
# 将您的文件上传或复制到此目录
```

**1. 配置 `.env` 文件:**
根据 `.env.example` 创建 `.env` 文件，并进行以下关键修改：

```bash
cp .env.example .env
vim .env
```

**必须修改的项：**
```dotenv
# .env

# --- 数据库配置 ---
# 数据库类型，保持为 dm
DB_TYPE=dm
# ！！！修改为您的达梦主库 IP！！！
DB_HOST=**************
# ！！！修改为您的达梦主库端口！！！
DB_PORT=5236
# ！！！修改为您的达梦数据库真实用户名和密码！！！
DB_USERNAME=SYSDBA
DB_PASSWORD=SYSDBA123
DB_DATABASE=DIFY
DB_PLUGIN_DATABASE=DIFY_PLUGIN

# --- Redis 配置 ---
# Redis Host 指向专用节点，但由于 Swarm 内部网络，我们仍然使用服务名
REDIS_HOST=redis
# ！！！修改为您的 Redis 密码！！！
REDIS_PASSWORD=difyai123456

# --- Celery Broker 配置 ---
# 确保密码与 REDIS_PASSWORD 一致
CELERY_BROKER_URL=redis://:difyai123456@redis:6379/1

# --- 向量数据库配置 ---
# 确保 VECTOR_STORE 是 qdrant
VECTOR_STORE=qdrant
# Qdrant URL 指向服务名
QDRANT_URL=http://qdrant:6333
# ！！！修改为您的 Qdrant API Key！！！
QDRANT_API_KEY=difyai123456

# --- 外部访问 URL 配置 ---
# ！！！这个URL必须是外部可访问的，指向Nginx的地址！！！
# 如果您有负载均衡器或DNS，请使用它。如果没有，可以用任一Manager节点的IP。
# 例如，使用主Manager节点IP和暴露的端口
FILES_URL=http://**************:7001
# 如果您在nginx前部署了LB或者域名，请修改成LB或者域名
# CONSOLE_API_URL, CONSOLE_WEB_URL, SERVICE_API_URL, APP_API_URL, APP_WEB_URL
# 也可以根据需要设置为 http://<your-domain-or-ip>:7001 这种格式

# --- Docker Compose Profiles ---
# 这一行会根据 VECTOR_STORE 的值自动启用 qdrant 服务
COMPOSE_PROFILES=${VECTOR_STORE:-qdrant}

# --- 端口暴露配置 ---
# 确认 Nginx 对外暴露的端口，与防火墙设置保持一致
EXPOSE_NGINX_PORT=7001
EXPOSE_NGINX_SSL_PORT=7013
```

**2. 修改 `docker-compose.yaml` 文件以适配 Swarm:**
这是最关键的一步。我们需要为服务添加 `deploy` 配置块。

**重要提示:** `docker-compose.yaml` 中使用了大量的本地路径挂载 (e.g., `- ../api:/app/api`)。在 Swarm 中，这意味着**这些本地文件必须存在于所有可能运行该服务的节点上**。对于 `api` 和 `worker` 服务，它们会部署在两台 Manager 节点上，所以您**必须确保 `**************` 和 `**************` 上具有完全相同的代码目录结构**。

一个更优的生产实践是**构建包含所有代码的自定义镜像**，然后推送到私有仓库，这样就不需要同步代码了。但为了快速部署，我们先按现有文件结构进行修改。

**修改后的 `docker-compose.yaml` 关键部分示例:**
```yaml
# ==================================================================
# WARNING: This file is a MODIFIED version for Docker Swarm.
# ==================================================================
version: "3.8" # Swarm 模式需要指定版本

# x-shared-env: &shared-api-worker-env ... (这部分保持不变)

services:
  api:
    image: dify-api:dm-1.01
    environment:
      # ... (保持不变)
    volumes:
      # ... (保持不变, 但要确保文件在两个Manager节点上都存在)
    networks:
      - ssrf_proxy_network
      - default
    deploy: # <--- 添加 deploy 块
      replicas: 2 # 部署2个实例，实现高可用
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.role == manager # 将 api 服务限制在 manager 节点上

  worker:
    image: dify-api:dm-1.01
    environment:
      # ... (保持不变)
    volumes:
      # ... (保持不变, 但要确保文件在两个Manager节点上都存在)
    networks:
      - ssrf_proxy_network
      - default
    deploy: # <--- 添加 deploy 块
      replicas: 2 # 部署2个实例
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.role == manager # 将 worker 服务限制在 manager 节点上
  
  web:
    image: dify-web:latest
    environment:
      # ... (保持不变)
    deploy: # <--- 添加 deploy 块
      replicas: 2 # 部署2个实例
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.role == manager # 将 web 服务限制在 manager 节点上

  redis:
    image: redis:6-alpine
    # ... (保持不变)
    deploy: # <--- 添加 deploy 块
      replicas: 1 # 有状态服务，只能有1个实例
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.role == redis-node # !!! 使用标签将其固定到 ************** !!!

  # ... 其他无状态服务如 sandbox, plugin_daemon, ssrf_proxy 也添加类似的 deploy 块 ...
  sandbox:
    # ...
    deploy:
      replicas: 2
      restart_policy: { condition: on-failure }
      placement: { constraints: ["node.role == manager"] }
  
  plugin_daemon:
    # ...
    deploy:
      replicas: 2
      restart_policy: { condition: on-failure }
      placement: { constraints: ["node.role == manager"] }
  
  ssrf_proxy:
    # ...
    deploy:
      replicas: 2
      restart_policy: { condition: on-failure }
      placement: { constraints: ["node.role == manager"] }
  
  nginx:
    image: nginx:latest
    # ... (保持不变)
    deploy: # <--- 添加 deploy 块
      replicas: 2 # 部署2个实例，作为高可用入口
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.role == manager # 部署在 manager 节点上
    ports:
      # 这个端口映射在Swarm中是全局的，流量会通过路由网格到达这两个Nginx实例
      - '${EXPOSE_NGINX_PORT:-80}:${NGINX_PORT:-80}'
      - '${EXPOSE_NGINX_SSL_PORT:-443}:${NGINX_SSL_PORT:-443}'

  # --- 向量数据库配置 ---
  qdrant:
    image: langgenius/qdrant:v1.7.3
    profiles:
      - qdrant
    volumes:
      - ./volumes/qdrant:/qdrant/storage
    environment:
      QDRANT_API_KEY: ${QDRANT_API_KEY:-difyai123456}
    deploy: # <--- 添加 deploy 块
      replicas: 1 # 有状态服务，只能有1个实例
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.role == qdrant-node # !!! 使用标签将其固定到 ************** !!!
  
  # ... 其他未使用的数据库服务 (weaviate, pgvector等) 保持原样，因为它们不会被部署 ...

networks:
  ssrf_proxy_network:
    driver: overlay # Swarm 模式下网络驱动应为 overlay
    internal: true
  milvus:
    driver: overlay
  opensearch-net:
    driver: overlay
    internal: true

volumes:
  # ... (保持不变)
```
**注意:** `container_name` 和 `restart: always` 属性在 `deploy` 模式下会被忽略，可以删除以保持文件整洁。`depends_on` 在 Swarm 中的作用有限，主要依赖 `healthcheck` 来保证启动顺序和健康状态。

---

### Phase 4: 部署应用

完成以上所有配置后，在主管理节点 `**************` 的 `/data/dify-swarm` 目录下，执行部署命令：

```bash
# 确保你在正确的目录下: /data/dify-swarm
# 使用 `dify-stack` 作为堆栈名称
docker stack deploy -c docker-compose.yaml dify-stack
```

Docker 会开始拉取镜像并在指定的节点上创建服务。这个过程可能需要几分钟。

---

### Phase 5: 验证与管理

**1. 查看服务状态:**
```bash
docker stack services dify-stack
```
理想情况下，`REPLICAS` 列应该显示 `2/2` (对于 api, web, nginx 等) 和 `1/1` (对于 redis, qdrant)。如果看到 `0/2`，说明服务启动失败，需要查看日志。

**2. 查看服务日志:**
如果某个服务启动失败，例如 `api`，可以使用以下命令查看其日志：
```bash
# 查看 dify-stack 堆栈中 api 服务的日志
docker service logs -f dify-stack_api
```

**3. 访问应用:**
一切正常后，您可以通过浏览器访问任一 Manager 节点的 Nginx 暴露端口：
*   `http://**************:7001`
*   `http://**************:7001`

Swarm 的路由网格（Routing Mesh）会自动将请求转发到健康的 Nginx 容器上。

**4. 更新服务:**
如果您修改了 `docker-compose.yaml` 或 `.env` 文件（例如，更新镜像版本或环境变量），只需重新执行部署命令即可：
```bash
docker stack deploy -c docker-compose.yaml dify-stack
```
Swarm 会自动进行滚动更新。

**5. 停止和移除应用:**
```bash
docker stack rm dify-stack
```

至此，您已经成功地使用 Docker Swarm 部署了一套高可用的 Dify 应用。



[root@host-10-134-252-146 ~]# docker node update --label-add role=redis-node xh0rbly1vzfy2ey6wnfbe4nch
xh0rbly1vzfy2ey6wnfbe4nch
[root@host-10-134-252-146 ~]# docker node update --label-add role=qdrant-node vdeg6r5e6384iin0bhw7e55cv
vdeg6r5e6384iin0bhw7e55cv
[root@host-10-134-252-146 ~]# docker node ls
ID                            HOSTNAME              STATUS    AVAILABILITY   MANAGER STATUS   ENGINE VERSION
768q3o4nyvhuerpocpzz930bx *   host-10-134-252-146   Ready     Active         Leader           27.5.1
r4votn2ixa76n7brkr9czq5sg     host-10-134-252-170   Ready     Active                          27.5.1
pgdi1swv4gsajhtd48mktok8w     host-10-134-252-198   Ready     Active         Reachable        27.5.1
i5ov7ltg67k2vemql7q85hipl     host-10-134-252-199   Ready     Active                          27.5.1
xh0rbly1vzfy2ey6wnfbe4nch     host-10-134-252-202   Ready     Active                          27.5.1
vdeg6r5e6384iin0bhw7e55cv     host-10-134-252-247   Ready     Active                          27.5.1
