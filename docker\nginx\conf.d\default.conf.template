# 请在dify项目原有的 nginx.conf 文件上进行修改

server {
    listen ${NGINX_PORT};
    server_name ${NGINX_SERVER_NAME};

    # ================= 新增部分 开始 =================
    # 捕获所有 /dify/ 的请求
    # 这个 location 块是新的总入口
    location /dify/ {
        # 使用 rewrite 指令将请求URI中的 /dify/ 前缀去掉
        # 例如: /dify/api/v1/workspaces -> /api/v1/workspaces
        # last 参数表示重写URL后，Nginx会重新开始匹配下面的 location 规则
        rewrite ^/dify/(.*)$ /$1 last;
    }
    # ================= 新增部分 结束 =================


    # ---- 以下是原有的 location 规则，保持不变 ----
    # Nginx重写URL后，会根据新的URL（已经没有/dify前缀）匹配到这些规则

    location /console/api {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /api {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /v1 {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /files {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /explore {
      proxy_pass http://web:3000;
      include proxy.conf;
    }

    location /e/ {
      proxy_pass http://plugin_daemon:5002;
      proxy_set_header Dify-Hook-Url $scheme://$host$request_uri;
      include proxy.conf;
    }

    location / {
      proxy_pass http://web:3000;
      include proxy.conf;
    }

    location /mcp {
      proxy_pass http://api:5001;
      include proxy.conf;
    }
    # placeholder for acme challenge location
    ${ACME_CHALLENGE_LOCATION}

    # placeholder for https config defined in https.conf.template
    ${HTTPS_CONFIG}
}
