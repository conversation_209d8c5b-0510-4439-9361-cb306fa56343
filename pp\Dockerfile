# 1. 使用一个官方的 ARM64 Python 基础镜像
# 这里的 python 版本应与你目标环境一致
FROM arm64v8/python:3.12-slim

# 2. 设置工作目录
WORKDIR /app

# 3. 安装编译所需要的工具
RUN apt-get update && apt-get install -y build-essential

# 4. 拷贝 ARM 架构的达梦客户端到镜像中
# 将 DM8_CLIENT_aarch64 目录拷贝到镜像的 /opt/dm_client_arm64
COPY DM8_CLIENT_aarch64 /opt/dm_client_arm64

# 5. 设置环境变量，让编译器和运行时能找到 libdm.so
ENV DM_HOME=/opt/dm_client_arm64
ENV LD_LIBRARY_PATH=$DM_HOME/lib
ENV C_INCLUDE_PATH=$DM_HOME/include
ENV CPLUS_INCLUDE_PATH=$DM_HOME/include

# 6. 拷贝 dmpython 源码并解压
COPY dmPython .

# 7. 进入源码目录并执行编译
WORKDIR /app/dmPython
# setup.py 会自动使用容器内的 C 编译器 (已经是 ARM 的) 和环境变量
RUN python setup.py build

# 这里只是为了让你看到编译产物，实际可以不用 CMD
CMD ls -R build
