\# CLAUDE.md



This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.



\## High-level Code Architecture



This is a monorepo containing a frontend web application and a backend API.



\-   `web/`: A Next.js application written in TypeScript. This is the frontend.

\-   `api/`: A Python Flask application that serves as the backend API.

\-   `docker/`: Contains Docker configurations for running the entire application stack using `docker-compose`. This is the recommended way to run the project locally.



\## Common Commands



The primary way to run this project for development is by using Docker Compose.



\### Running the Application (Docker)



1\.  Navigate to the `docker` directory: `cd docker`

2\.  Copy the example environment file: `cp .env.example .env`

3\.  Start the services: `docker compose up -d`

4\.  Access the application at `http://localhost/install`.



\### Web (Frontend)



The frontend is located in the `web/` directory and uses `pnpm` as the package manager.



\-   \*\*Install dependencies:\*\* `pnpm install`

\-   \*\*Run development server:\*\* `pnpm dev`

\-   \*\*Build for production:\*\* `pnpm build`

\-   \*\*Run production server:\*\* `pnpm start`

\-   \*\*Lint code:\*\* `pnpm lint`

\-   \*\*Run tests:\*\* `pnpm test`



\### API (Backend)



The backend is a Flask application located in the `api/` directory. Dependencies are managed with `uv` and are defined in `pyproject.toml`. It is typically run via Gunicorn within the Docker container.



\### Building and Pushing Docker Images



The `Makefile` in the root directory provides helper scripts for building and pushing Docker images.



\-   \*\*Build web image:\*\* `make build-web`

\-   \*\*Build api image:\*\* `make build-api`

\-   \*\*Build all images:\*\* `make build-all`

\-   \*\*Push web image:\*\* `make push-web`

\-   \*\*Push api image:\*\* `make push-api`

\-   \*\*Push all images:\*\* `make push-all`



