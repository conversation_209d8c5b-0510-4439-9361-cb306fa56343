## dmDjango

Django是基于Python的Web应用程序框架，dmDjango是DM提供的Django连接DM数据库的驱动，当前版本为 `3.0.5` ，API详见安装目录下的 `《DM8_dmPython使用手册》` ，目前用于适配 `3.0` 以上版本的Django。

### ChangeLogs

#### dmDjango v3.0.5(2025-01-15)

* 修复了连接数据库时需要赋予连接用户 `SOI` 角色的安全问题
* 修复了当表名或者列名带单引号情况，无法正确查询到表或列信息的问题
* 修复了使用 `pip uninstall` 无法卸载包的问题
* 修复了安装时候警告未声明 `zip_safe` 的问题

#### dmDjango v3.0.4(2024-09-25)

* 修复了在Linux环境下使用Django连接DM，处理 `distinct` 和 `cast` 时，语法有误的问题
* 修复了处理数据为时间类型时绑定为 `week` 时出现错误现象

#### dmDjango v3.0.3(2024-08-09)

* 修正了代码格式，移除了冗余代码

#### dmDjango v3.0.2(2024-04-10)

* 新增了对于 `Django 5.0 ` 新版本的支持，修复了部分函数报错问题
* 修复了新创建用户无法使用的问题，新创建的用户将默认获得 `SOI角色` 权限

#### dmDjango v3.0.1(2023-03-15)

* 新增了对于 `Django 4.1` 新版本的支持，修复了部分函数报错问题

#### dmDjango v3.0.0(2023-02-28)

* 新建框架适配包，用于兼容 `3.0` 以上版本的Django框架