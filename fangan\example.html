<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>架构图复原</title>
    <style>
        /* 全局和基础样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding: 20px;
            font-size: 14px;
        }

        .architecture-diagram {
            display: flex;
            gap: 10px;
            background-color: #fff;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            min-width: 1200px;
        }

        .main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        /* 层级样式 */
        .layer {
            display: flex;
            border-bottom: 3px solid #1a5ca3;
            margin-bottom: 10px;
        }
        .layer:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .layer-label {
            writing-mode: horizontal-tb; /* 保持水平文字 */
            background-color: #e3f2fd;
            color: #1a5ca3;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
            width: 80px;
            flex-shrink: 0;
            border-right: 1px solid #bbdefb;
        }

        .layer-content {
            flex-grow: 1;
            padding: 15px;
            background-color: #f5faff;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        /* 通用块样式 */
        .block {
            background-color: #cce7ff;
            border: 1px solid #a3c9f5;
            border-radius: 4px;
            padding: 8px 12px;
            text-align: center;
            white-space: nowrap;
        }

        .row {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        /* 各层级内部细节 */
        /* 展示层 */
        .presentation-layer .row:last-child {
            margin-top: 5px;
        }

        /* 计算层 */
        .computation-layer .dotted-group {
            border: 2px dashed #a3c9f5;
            border-radius: 8px;
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .computation-layer .wide-block {
            flex-grow: 1;
            max-width: 90%;
            margin: 0 auto;
        }
        .computation-layer .sub-text {
            display: block;
            font-size: 0.8em;
            color: #555;
            margin-top: 2px;
        }

        /* 存储层 */
        .storage-layer .db-container {
            border: 2px dashed #a3c9f5;
            border-radius: 8px;
            padding: 15px 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            min-width: 130px;
        }
        .storage-layer .db-icon {
            width: 50px;
            height: 50px;
            background-color: #cce7ff;
            border: 1px solid #a3c9f5;
            border-radius: 50%; /* 简化为圆形图标 */
            margin-bottom: 5px;
        }
        .storage-layer .db-name {
            font-weight: bold;
        }
        .storage-layer .db-type {
            font-size: 0.9em;
            color: #555;
        }

        /* 数据集成层 */
        .integration-layer .layer-content {
           gap: 15px;
        }
        .integration-layer .block {
            width: 100%;
            padding: 12px;
        }

        /* 箭头连接器 */
        .connector {
            display: flex;
            justify-content: center;
            gap: 20%;
            padding: 5px 0;
            margin-top: -10px;
            margin-bottom: -10px;
            position: relative;
            z-index: 1;
        }
        .connector .arrow {
            color: #1a5ca3;
            font-size: 24px;
            font-weight: bold;
        }

        /* 右侧管控层 */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 150px;
            flex-shrink: 0;
        }

        .sidebar .sidebar-title {
            background-color: #1a5ca3;
            color: white;
            font-weight: bold;
            text-align: center;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 5px;
        }

        .sidebar .block {
             width: 100%;
        }
        
    </style>
</head>
<body>

<div class="architecture-diagram">
    <div class="main-content">
        <!-- 展示层 -->
        <div class="layer presentation-layer">
            <div class="layer-label">展示层</div>
            <div class="layer-content">
                <div class="row">
                    <div class="block">Html5</div>
                    <div class="block">Css</div>
                    <div class="block">JavaScript</div>
                    <div class="block">D3.js</div>
                    <div class="block">jQuery.js</div>
                </div>
                <div class="row" style="justify-content: flex-start; margin-left: 20%;">
                    <div class="block">VUE</div>
                </div>
            </div>
        </div>

        <!-- 共享应用 -->
        <div class="layer application-layer">
            <div class="layer-label">共享应用</div>
            <div class="layer-content">
                <div class="row">
                    <div class="block">文本智能解析平台</div>
                    <div class="block">智能共享服务</div>
                    <div class="block">图谱管理</div>
                    <div class="block">数据运营</div>
                    <div class="block">搜索服务</div>
                </div>
            </div>
        </div>

        <!-- 计算层 -->
        <div class="layer computation-layer">
            <div class="layer-label">计算层</div>
            <div class="layer-content">
                <div class="dotted-group">
                    <div class="row">
                        <div class="block">自然语言处理</div>
                        <div class="block">大模型调度</div>
                        <div class="block">智能计算</div>
                        <div class="block">统计分析</div>
                        <div class="block">规则引擎</div>
                        <div class="block">搜索引擎</div>
                    </div>
                    <div class="row">
                        <div class="block wide-block">任务调度</div>
                    </div>
                </div>
                <div class="row">
                    <div class="block">流式计算<span class="sub-text">Flink</span></div>
                    <div class="block">流式计算<span class="sub-text">Spark</span></div>
                </div>
            </div>
        </div>

        <!-- 存储层 -->
        <div class="layer storage-layer">
            <div class="layer-label">存储层</div>
            <div class="layer-content">
                <div class="row" style="justify-content: space-around; align-items: stretch;">
                    <div class="db-container">
                        <div class="db-icon"></div>
                        <div class="db-name">分布式图数据库</div>
                        <div class="db-type">GraphN</div>
                    </div>
                    <div class="db-container">
                        <div class="db-icon"></div>
                        <div class="db-name">消息队列</div>
                        <div class="db-type">MQ</div>
                    </div>
                    <div class="db-container">
                        <div class="db-icon"></div>
                        <div class="db-name">分布式缓存数据库</div>
                        <div class="db-type">Redis</div>
                    </div>
                    <div class="db-container">
                        <div class="db-icon"></div>
                        <div class="db-name">分布式索引数据库</div>
                        <div class="db-type">ES</div>
                    </div>
                    <div class="db-container">
                        <div class="db-icon"></div>
                        <div class="db-name">分布式文件服务器</div>
                        <div class="db-type">MINIO</div>
                    </div>
                    <div class="db-container">
                        <div class="db-icon"></div>
                        <div class="db-name">关系型数据库</div>
                        <div class="db-type">RDBMS</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 箭头 -->
        <div class="connector">
            <div class="arrow">↕</div>
            <div class="arrow">↕</div>
        </div>

        <!-- 数据集成 -->
        <div class="layer integration-layer">
            <div class="layer-label">数据集成</div>
            <div class="layer-content">
                <div class="block">分布式ETL</div>
                <div class="block">统一采集: FTP/Socket/Webservice/其他数据接口</div>
            </div>
        </div>
    </div>

    <!-- 统一管控层 -->
    <div class="sidebar">
        <div class="sidebar-title">统一管控层</div>
        <div class="block">监控平台</div>
        <div class="block">报警通知</div>
        <div class="block">日志平台</div>
        <div class="block">镜像服务</div>
        <div class="block">......</div>
    </div>
</div>

</body>
</html>