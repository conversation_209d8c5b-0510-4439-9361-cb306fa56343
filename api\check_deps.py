
import tomllib
import re
import sys

try:
    from packaging.specifiers import SpecifierSet
    from packaging.version import Version
except ImportError:
    print("错误: 找不到 'packaging' 库。请通过 'pip install packaging' 安装。")
    sys.exit(1)

# 用于解析 "package[extra]~=1.2.3" 格式的依赖字符串的正则表达式
DEP_REGEX = re.compile(r"^\s*([a-zA-Z0-9_.-]+)(\[[a-zA-Z0-9_,-]+\])?\s*([~<>=!].*)?$")

def parse_dependency(dep_string):
    """将依赖字符串解析为名称和版本说明符。"""
    match = DEP_REGEX.match(dep_string)
    if not match:
        # 处理只有名称没有版本说明符的情况
        return dep_string.strip(), ""
    
    name, _extras, specifier = match.groups()
    return name.strip(), specifier.strip() if specifier else ""

def main():
    pyproject_path = 'pyproject.toml'
    lockfile_path = 'uv.lock'

    try:
        with open(pyproject_path, 'rb') as f:
            pyproject_data = tomllib.load(f)
    except FileNotFoundError:
        print(f"错误: {pyproject_path} 未找到。")
        return
    except Exception as e:
        print(f"读取 {pyproject_path} 时出错: {e}")
        return

    try:
        with open(lockfile_path, 'rb') as f:
            lockfile_data = tomllib.load(f)
    except FileNotFoundError:
        print(f"错误: {lockfile_path} 未找到。")
        return
    except Exception as e:
        print(f"读取 {lockfile_path} 时出错: {e}")
        return

    # 从 uv.lock 创建包名到版本的映射
    locked_packages = {pkg['name']: pkg['version'] for pkg in lockfile_data.get('package', [])}

    # 从 pyproject.toml 获取要检查的依赖
    deps_to_check = pyproject_data.get('project', {}).get('dependencies', [])
    
    mismatches = []
    missing = []

    for dep_string in deps_to_check:
        name, specifier_str = parse_dependency(dep_string)
        
        # 特殊处理：bs4 是 beautifulsoup4 的别名
        if name == 'bs4':
            if 'beautifulsoup4' not in locked_packages:
                 missing.append(f"{dep_string} (作为 beautifulsoup4)")
            continue

        if name not in locked_packages:
            missing.append(dep_string)
            continue

        if not specifier_str:
            # 没有指定版本，只要存在即可
            continue

        locked_version_str = locked_packages[name]
        
        try:
            specifiers = SpecifierSet(specifier_str)
            locked_version = Version(locked_version_str)
        except Exception as e:
            mismatches.append(f"无法解析 '{dep_string}' 的版本/说明符: {e}")
            continue

        if locked_version not in specifiers:
            mismatches.append(f"'{dep_string}': 锁文件版本 {locked_version} 与说明符不匹配。")

    print("依赖检查报告:")
    print("========================")

    if missing:
        print("\n[!] pyproject.toml 中存在但 uv.lock 中缺失的依赖:")
        for item in missing:
            print(f"  - {item}")
    
    if mismatches:
        print("\n[!] pyproject.toml 和 uv.lock 之间存在版本不匹配:")
        for item in mismatches:
            print(f"  - {item}")

    if not missing and not mismatches:
        print("\n[+] pyproject.toml 中的所有主依赖似乎都已正确锁定。")
        print("问题可能与传递性依赖、依赖组或平台有关。")
    
    if 'bs4' in [parse_dependency(d)[0] for d in deps_to_check]:
        print("\n注意: 'bs4' 存在于 pyproject.toml 中。这是 'beautifulsoup4' 的一个别名，通常是多余的。这很可能是锁文件冲突的根源。")


if __name__ == "__main__":
    main()
