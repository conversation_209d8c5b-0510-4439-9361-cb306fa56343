<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify 功能架构图</title>
    <style>
        /* 全局和基础样式 (参考 example.html) */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding: 20px;
            font-size: 14px;
        }

        .architecture-diagram {
            display: flex;
            flex-direction: column; /* 改为垂直堆叠层 */
            gap: 10px;
            background-color: #fff;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            min-width: 1200px;
        }

        /* 层级样式 */
        .layer {
            display: flex;
            border: 2px solid #1a5ca3;
            border-radius: 6px;
            overflow: hidden; /* 确保子元素圆角生效 */
        }

        .layer-label {
            writing-mode: horizontal-tb;
            background-color: #e3f2fd;
            color: #1a5ca3;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
            width: 120px; /* 适当加宽以容纳文字 */
            flex-shrink: 0;
            border-right: 1px solid #bbdefb;
            text-align: center;
        }

        .layer-content {
            flex-grow: 1;
            padding: 15px;
            background-color: #f5faff;
            display: flex;
            flex-direction: column;
            gap: 15px; /* 组之间的间距 */
        }
        
        /* 组样式 */
        .group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .group-title {
            font-weight: bold;
            font-size: 1.1em;
            color: #1a5ca3;
            padding-bottom: 5px;
            border-bottom: 1px solid #bbdefb;
        }
        
        .row {
            display: flex;
            justify-content: flex-start; /* 左对齐 */
            align-items: flex-start; /* 顶部对齐 */
            gap: 10px;
            flex-wrap: wrap;
        }
        
        /* 通用块样式 */
        .block {
            background-color: #cce7ff;
            border: 1px solid #a3c9f5;
            border-radius: 4px;
            padding: 8px 12px;
            text-align: center;
            white-space: normal; /* 允许换行 */
            min-width: 120px;
            flex-grow: 1; /* 允许块伸展 */
        }
        
        .block strong {
            font-weight: bold;
            display: block;
            margin-bottom: 4px;
            color: #1a5ca3;
        }

    </style>
</head>
<body>

<div class="architecture-diagram">

    <!-- 展现层 -->
    <div class="layer">
        <div class="layer-label">展现层 (Frontend)</div>
        <div class="layer-content">
            <div class="group">
                <div class="group-title">用户界面 (Next.js)</div>
                <div class="row">
                    <div class="block"><strong>应用工作室:</strong> 可视化 Prompt 编排、工作流</div>
                    <div class="block"><strong>知识库:</strong> RAG 数据集管理与处理</div>
                    <div class="block"><strong>AI Agent:</strong> 智能体创建与调试</div>
                    <div class="block"><strong>可观测性:</strong> 日志查看、成本分析</div>
                    <div class="block"><strong>模型管理:</strong> LLM 模型配置与微调</div>
                </div>
            </div>
            <div class="group">
                <div class="group-title">应用接入 (BaaS)</div>
                <div class="row">
                    <div class="block">Web App / API 接口</div>
                    <div class="block">嵌入式聊天机器人</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 应用层 -->
    <div class="layer">
        <div class="layer-label">应用层 (Backend)</div>
        <div class="layer-content">
            <div class="group">
                <div class="group-title">API 服务 (Flask / Gunicorn)</div>
                <div class="row">
                    <div class="block"><strong>工作流引擎:</strong> 执行和管理 AI 工作流</div>
                    <div class="block"><strong>RAG 引擎:</strong> 文档索引、分块、检索</div>
                    <div class="block"><strong>Agent 框架:</strong> 工具调用、多步推理</div>
                    <div class="block"><strong>LLM 抽象层:</strong> 统一调度各类语言模型</div>
                    <div class="block"><strong>权限与多租户</strong></div>
                </div>
            </div>
            <div class="group">
                <div class="group-title">异步任务队列 (Celery / Redis)</div>
                <div class="row">
                    <div class="block">知识库文档处理</div>
                    <div class="block">批量任务执行</div>
                    <div class="block">日志与数据上报</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据 & 集成层 -->
    <div class="layer">
        <div class="layer-label">数据 & 集成层</div>
        <div class="layer-content">
            <div class="group">
                <div class="group-title">数据存储</div>
                <div class="row">
                    <div class="block"><strong>结构化数据:</strong> 达梦数据库 (应用、用户、日志元数据)</div>
                    <div class="block"><strong>缓存与队列:</strong> Redis</div>
                    <div class="block"><strong>非结构化数据:</strong> 对象存储 (FS, S3) (用户上传的原始文件)</div>
                    <div class="block"><strong>向量数据:</strong> 向量数据库 (Weaviate, Qdrant) (文本嵌入向量)</div>
                </div>
            </div>
            <div class="group">
                <div class="group-title">外部服务集成</div>
                <div class="row">
                    <div class="block"><strong>LLM 提供商:</strong> OpenAI, 人工智能平台网关</div>
                    <div class="block"><strong>工具集:</strong> 自定义工具等</div>
                </div>
            </div>
        </div>
    </div>

</div>

</body>
</html>
